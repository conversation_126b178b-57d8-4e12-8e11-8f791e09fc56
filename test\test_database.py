from flask import Flask, jsonify, request
from flask_cors import CORS
from flask_pymongo import PyMongo
from pymongo.errors import ConnectionFailure, OperationFailure
from pymongo.collection import Collection
from pymongo.cursor import Cursor
from pymongo.results import InsertOneResult
from bson import ObjectId
from typing import List, Dict, Any, Optional, cast
import traceback

app: Flask = Flask(__name__)
app.secret_key = "secret"
CORS(app)

# 配置 MongoDB URI - 添加数据库名称
app.config["MONGO_URI"] = "mongodb://localhost:27017/web_user"

# 初始化 PyMongo 实例
mongo: PyMongo = PyMongo(app)

@app.route('/insert_data', methods=['POST', 'GET'])
def insert_data_route():
    """
    处理 '/insert_data' 路由的 POST/GET 请求，向 MongoDB 插入数据。
    
    路由:
        POST /insert_data - 接收 JSON 格式数据并插入到 'items' 集合
        GET /insert_data - 插入默认测试数据到 'items' 集合
    
    返回:
        JSON 响应:
            - 成功: 201 状态码，包含插入成功的消息和 inserted_id
            - 失败: 相应错误状态码和错误信息
    
    异常处理:
        - 数据库连接失败 (ConnectionFailure)
        - MongoDB 操作失败 (OperationFailure)
        - 其他未预期异常
    
    注意事项:
        1. 需要确保 MongoDB 连接已初始化
        2. POST 请求必须携带有效的 JSON 数据
        3. 使用类型注解确保类型安全
    """
    try:
        # 确保 mongo.db 不为 None，并明确指定集合类型
        if mongo.db is None:
            return jsonify({"error": "数据库连接未初始化"}), 500
            
        items_collection: Collection[Dict[str, Any]] = mongo.db['items']

        data_to_insert: Dict[str, Any]

        if request.method == 'POST' and request.is_json:
            # 使用 cast 来明确告诉 Pylance request.json 的类型
            json_payload: Optional[Any] = request.json
            
            if json_payload is None:
                return jsonify({"error": "JSON 负载为空"}), 400
                
            if not isinstance(json_payload, dict):
                return jsonify({"error": "无效的 JSON 负载：应为一个对象"}), 400
                    
            # 使用 cast 确保类型正确
            data_to_insert = cast(Dict[str, Any], json_payload)
        else:
            data_to_insert = {
                "name": "示例物品",
                "quantity": 10,
                "tags": ["示例", "测试", "flask_pymongo"]
            }

        # 明确指定 insert_one 的返回类型
        insert_result: InsertOneResult = items_collection.insert_one(data_to_insert)

        if insert_result.acknowledged:
            # 确保 inserted_id 存在并转换为字符串
            inserted_id_str: str = str(insert_result.inserted_id)
            return jsonify({
                "message": "数据插入成功！",
                "inserted_id": inserted_id_str
            }), 201
        else:
            return jsonify({"error": "数据插入未被 MongoDB 确认"}), 500

    except ConnectionFailure:
        return jsonify({"error": "无法连接到 MongoDB。服务器是否正在运行？"}), 500
    except OperationFailure as e:
        return jsonify({"error": f"MongoDB 操作失败: {str(e)}"}), 500
    except Exception as e:
        app.logger.error(f"/insert_data 发生意外错误: {e}", exc_info=True)
        return jsonify({"error": f"发生意外错误: {str(e)}"}), 500

@app.route('/view_data', methods=['GET'])
def view_data_route():
    try:
        # 确保 mongo.db 不为 None
        if mongo.db is None:
            return jsonify({"error": "数据库连接未初始化"}), 500
            
        items_collection: Collection[Dict[str, Any]] = mongo.db['items']
        
        # 明确指定 find() 的返回类型
        all_items_cursor: Cursor[Dict[str, Any]] = items_collection.find()
        
        all_items_list: List[Dict[str, Any]] = []
        
        # 遍历游标，item 现在有明确的类型
        item: Dict[str, Any]
        for item in all_items_cursor:
            # 安全地处理 _id 字段
            if '_id' in item and isinstance(item['_id'], ObjectId):
                item['_id'] = str(item['_id'])
            all_items_list.append(item)
            
        return jsonify(all_items_list), 200
        
    except ConnectionFailure:
        return jsonify({"error": "无法连接到 MongoDB"}), 500
    except Exception as e:
        app.logger.error(f"/view_data 发生意外错误: {e}", exc_info=True)
        return jsonify({"error": f"发生错误: {str(e)}"}), 500

def test_direct_insertion() -> None:
    """测试直接插入功能"""
    print("尝试直接插入 (在 Flask 请求之外)...")
    try:
        # 确保在应用上下文中并且 mongo.db 不为 None
        if mongo.db is None:
            print("错误：数据库连接未初始化")
            return
            
        direct_test_collection: Collection[Dict[str, Any]] = mongo.db['items_direct_test']
        test_data: Dict[str, Any] = {
            "source": "direct_script", 
            "value": "test_value_123"
        }
        
        result: InsertOneResult = direct_test_collection.insert_one(test_data)
        
        if result.acknowledged:
            inserted_id_str: str = str(result.inserted_id)
            print(f"直接插入成功！ID: {inserted_id_str}")
        else:
            print("直接插入失败或未被确认")
            
    except ConnectionFailure:
        print("直接插入失败：无法连接到 MongoDB。它在运行吗？")
    except Exception as e:
        print(f"直接插入时出错: {e}\n{traceback.format_exc()}")

if __name__ == '__main__':
    with app.app_context():
        test_direct_insertion()
        print("-" * 30)

    print("启动 Flask 应用...")
    print("尝试访问: http://127.0.0.1:5000/insert_data (GET 请求)")
    print("或者发送带有 JSON 数据的 POST 请求到 http://127.0.0.1:5000/insert_data")
    print("然后检查: http://127.0.0.1:5000/view_data")
    app.run(debug=True)