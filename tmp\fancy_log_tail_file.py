import os
import time
import argparse
from collections import deque
from rich.console import Console
from rich.layout import Layout
from rich.live import Live
from rich.panel import Panel
from rich.text import Text
from typing import Dict, Deque, cast
import logging

# --- 配置 ---
DEFAULT_LOG_FILE = os.path.join("../log", "radar.log")
MAX_LOG_LINES = 100
POLL_INTERVAL = 0.1

# --- 面板样式和分类规则 (基于 logger 名称) ---
PANELS_CONFIG = {
    "api": {
        "title": "[bold blue]API Logs (arcweb.api)[/bold blue]",
        "color": "blue",
        "logger_name": "arcweb.api",
    },
    "server": {
        "title": "[bold green]雷达服务器 Logs (arcweb.server)[/bold green]",
        "color": "green",
        "logger_name": "arcweb.server",
    },
    "simulator": {
        "title": "[bold magenta]模拟器 Logs (arcweb.simulator)[/bold magenta]",
        "color": "magenta",
        "logger_name": "arcweb.simulator",
    },
    "client": {
        "title": "[bold yellow]雷达客户端/雷达 Logs (arcweb.radar)[/bold yellow]",
        "color": "yellow",
        "logger_name": "arcweb.radar",
    },
    "worker": {
        "title": "[bold bright_cyan]Worker Logs (arcweb.worker)[/bold bright_cyan]",
        "color": "bright_cyan",
        "logger_name": "arcweb.worker",
    },
    "other": {
        "title": "[bold grey70]其他/未知来源 Logs[/bold grey70]",
        "color": "grey70",
        "logger_name": "",  # 默认分类
    },
}

# --- UI 布局定义 ---
console = Console()
layout = Layout()
layout.split(Layout(name="header", size=3), Layout(ratio=1, name="main"))
layout["main"].split_row(Layout(name="left"), Layout(name="right"))
layout["left"].split_column(
    Layout(name="api"), Layout(name="simulator"), Layout(name="worker")
)
layout["right"].split_column(
    Layout(name="server"), Layout(name="client"), Layout(name="other")
)


def parse_log_line(line: str) -> tuple | None:
    """尝试将标准日志行解析为其四个主要部分"""
    try:
        parts = line.split(" - ", 3)
        if len(parts) == 4:
            return tuple(p.strip() for p in parts)
        return None
    except Exception:
        return None


def categorize_by_name(logger_name: str) -> str:
    """根据 logger 名称返回其所属的面板类别"""
    for panel_key, config in PANELS_CONFIG.items():
        if panel_key == "other":
            continue
        if logger_name.startswith(config["logger_name"]):
            return panel_key
    return "other"


def format_display_line(line: str) -> str:
    """根据日志来源智能格式化日志行以供显示"""
    parsed_parts = parse_log_line(line)

    # 如果无法解析，则按原样返回
    if not parsed_parts:
        return line

    asctime, name, level, message = parsed_parts

    # 规则 1: 特殊处理 werkzeug 日志
    if name == "werkzeug":
        try:
            # 提取 "REQUEST" STATUS - 部分
            http_part_start = message.find('"')
            if http_part_start != -1:
                http_part = message[http_part_start:]
                # 移除最后的 " -"
                if http_part.endswith(" -"):
                    http_part = http_part[:-2].strip()
                return f"{asctime} - {level} - {http_part}"
        except Exception:
            return line  # 如果解析失败，返回原始行

    # 规则 2: 如果消息自带标签，则省略 logger name
    # 检查消息是否以 "[任意字符]" 的形式开头
    if message.startswith("[") and "]" in message:
        return f"{asctime} - {level} - {message}"

    # 默认情况：返回原始行
    return line


def create_log_panel(category_name: str, logs: deque) -> Panel:
    """创建并更新一个面板，并高亮日志级别"""
    config = PANELS_CONFIG[category_name]

    log_text = Text("\n".join(logs), style="white", no_wrap=True)
    if not logs:
        log_text = Text("暂无日志 ...", style="dim")

    log_text.highlight_regex(r"INFO", "bold green")
    log_text.highlight_regex(r"DEBUG", "bold cyan")
    log_text.highlight_regex(r"WARNING", "bold yellow")
    log_text.highlight_regex(r"ERROR", "bold red")
    log_text.highlight_regex(r"CRITICAL", "bold white on red")

    return Panel(
        log_text,
        title=config["title"],
        border_style=config["color"],
        title_align="left",
    )


def main(log_file_path: str):
    """主函数，运行实时逆序日志监控界面"""
    if not os.path.exists(log_file_path):
        console.print(f"[bold red]错误: 日志文件 '{log_file_path}' 未找到。[/bold red]")
        return

    logs: Dict[str, Deque[logging.LogRecord]] = {
        name: deque(maxlen=MAX_LOG_LINES) for name in PANELS_CONFIG
    }

    header_text = Text(
        f"实时雷达系统日志监控 - 文件: {log_file_path}", justify="center"
    )
    layout["header"].update(Panel(header_text, style="bold blue"))

    # 初始填充
    with open(log_file_path, "r", encoding="utf-8", errors="ignore") as file:
        all_lines = file.readlines()
        for raw_line in reversed(all_lines):
            line = raw_line.strip()
            if not line:
                continue

            parsed = parse_log_line(line)
            category = categorize_by_name(parsed[1]) if parsed else "other"

            if len(logs[category]) < MAX_LOG_LINES:
                display_line = format_display_line(line)
                logs[category].appendleft(cast(logging.LogRecord, display_line))

    # 实时轮询
    with Live(
        layout,
        console=console,
        screen=True,
        redirect_stderr=False,
        refresh_per_second=10,
    ) as live:
        try:
            for name, log_deque in logs.items():
                panel = create_log_panel(name, log_deque)
                layout[name].update(panel)

            with open(log_file_path, "r", encoding="utf-8", errors="ignore") as file:
                file.seek(0, 2)
                while True:
                    line = file.readline()
                    if not line:
                        time.sleep(POLL_INTERVAL)
                        continue

                    raw_line = line.strip()
                    if not raw_line:
                        continue

                    parsed = parse_log_line(raw_line)
                    category = categorize_by_name(parsed[1]) if parsed else "other"

                    display_line = format_display_line(raw_line)
                    logs[category].appendleft(cast(logging.LogRecord, display_line))

                    panel = create_log_panel(category, logs[category])
                    layout[category].update(panel)

        except KeyboardInterrupt:
            live.stop()
            console.print("[bold red]监控已停止。[/bold red]")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="美化并分类显示日志文件。")
    parser.add_argument(
        "-f",
        "--file",
        dest="log_file",
        default=DEFAULT_LOG_FILE,
        help=f"指定要监控的日志文件路径。默认为: {DEFAULT_LOG_FILE}",
    )
    args = parser.parse_args()

    main(args.log_file)
