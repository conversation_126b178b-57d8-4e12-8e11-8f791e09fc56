# -*- coding: utf-8 -*-

# ==============================================================================
# 核心库导入
# ==============================================================================
import asyncio
import hashlib
import multiprocessing as mp
import os
import socket
import struct
import threading
import time
from asyncio import Event as AsyncEvent
from asyncio import Queue as AsyncQueue
from collections import namedtuple
from datetime import datetime
from enum import IntEnum
from functools import wraps
from logging import INFO, basicConfig, getLogger
from typing import (
    Any,
    Callable,
    Dict,
    List,
    Literal,
    Optional,
    Tuple,
    TypeGuard,
    cast,
    overload,
)

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import snappy  # type: ignore
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
from pymongo import MongoClient
from motor.motor_asyncio import (
    AsyncIOMotorClient,
    AsyncIOMotorCollection,
    AsyncIOMotorDatabase,
)
from pydantic import BaseModel, Field, ValidationError, model_validator
from scipy.interpolate import griddata  # type: ignore
from tifffile import imread, imwrite  # type: ignore

# ==============================================================================
# 日志配置
# ==============================================================================
logger = getLogger(__name__)
if not os.path.exists("log"):
    os.makedirs("log")

basicConfig(
    filename="log/radar_asyncio_final.log",
    level=INFO,
    format="%(asctime)s - [%(levelname)s] - (%(threadName)s) - %(message)s",
    encoding="utf-8",
)

# ==============================================================================
# 全局配置
# ==============================================================================
BASE_FILE_PATH = os.getcwd()
HOST: str = "127.0.0.1"
PORT: int = 1030
MONGO_URI = f"mongodb://{HOST}:27017/"

# ASYNCIO 重构: 使用异步的 Motor 客户端
client: AsyncIOMotorClient = AsyncIOMotorClient(MONGO_URI)
db_base_data: AsyncIOMotorDatabase = client["base_data"]
radar_collection: AsyncIOMotorCollection = db_base_data["radar"]


# ==============================================================================
# 命令和枚举定义
# ==============================================================================
class CommandHeader(IntEnum):
    PLATFORM = 0x5A5A
    RADAR = 0x3C3C


class CommandCode(IntEnum):
    SETPARAMETER = 0x00
    QUERYPARAMETER = 0x01
    QUERYRADARSTATE = 0x02
    WORKCONTROL = 0x03
    UPLOADDATA = 0x40
    ATMOSPHERECORRECTION = 0x05
    LOGFILE = 0x08


class SetParamter(IntEnum):
    SCENE = 0x00
    ALGORITHM = 0x01


class QueryParameter(IntEnum):
    SCENE = 0x00
    ALGORITHM = 0x01


class QueryRadarState(IntEnum):
    STATE = 0x00


class WorkCommand(IntEnum):
    START = 0x00
    STOP = 0x01
    REBOOT = 0x02


class AtmosphericCorrectionCommand(IntEnum):
    DISABLE = 0x00
    ENABLE = 0x01


class LogCommand(IntEnum):
    QUERY = 0x00
    DOWNLOAD = 0x01


class UploadDataType(IntEnum):
    INFO = 0x00
    IMAGE = 0x01
    DEFO = 0x02
    CONFIDENCE = 0x03
    MOVINGTARGET = 0x04
    LOG = 0x05
    TIME = 0x06


# ==============================================================================
# Pydantic 数据模型
# ==============================================================================
class SceneParameter(BaseModel):
    """
    一个基于提供的技术表格精确定义的场景参数Pydantic模型。
    该模型包含了详细的字段描述、别名、类型和严格的验证规则。
    """

    # model_config 用于Pydantic V2版本，等同于旧版的 class Config
    model_config = {
        "populate_by_name": True,  # 允许通过字段名和别名两种方式进行赋值
        "extra": "forbid",  # 禁止传入未在模型中定义的额外字段
    }

    # =================================
    # 扫描参数 (Scanning Parameters)
    # =================================
    rotation_rate: Literal[0, 20, 30, 50, 60] = Field(
        alias="RoRate",
        description="正扫转台转速。0: 停止; 20: 2rpm; 30: 1rpm; 50: 1/4rpm; 60: 1/16rpm。",
    )
    rotation_angle_start_degrees: float = Field(
        alias="RotAngBgn",
        description="转台起始扫描角，格式为 dd.dd°。从转台零位开始，顺时针方向，按最小步进量化。",
    )
    rotation_angle_end_degrees: float = Field(
        alias="RotAngEnd",
        description="转台结束扫描角，格式为 dd.dd°。从转台零位开始，顺时针方向，按最小步进量化。",
    )
    range_min_meters: float = Field(
        alias="RangeMin", ge=0, description="回波最近距离，单位为米。"
    )
    range_max_meters: float = Field(
        alias="RangeMax", ge=0, description="回波最远距离，单位为米。"
    )
    radar_mode: Literal[0, 1] = Field(
        alias="RadarMode",
        description="高分、性能模式切换。0：高分模式，1：性能模式。占用整数低字节。",
    )
    point_selection_threshold: int = Field(
        alias="PS_TD",
        ge=0,
        le=40,
        description="PS筛选门限，范围0-40，数值越大，PS点越多。",
    )

    # =================================
    # 输出参数 (Output Parameters)
    # =================================
    start_dwell_time_seconds: int = Field(
        alias="StartStopTime", ge=0, description="起点停留时间，单位为秒。"
    )
    end_dwell_time_seconds: int = Field(
        alias="EndStopTime", ge=0, description="终点停留时间，单位为秒。"
    )
    is_scatter_image_enabled: Literal[0, 1] = Field(
        alias="ScatImageEn", description="散射图像是否有效。0: 无效; 1: 有效。"
    )
    scatter_image_decimation: int = Field(
        default=0,
        alias="ScatImageDec",
        ge=0,
        description="散射图像帧抽取。0: 无效; 1及以上: 抽取比。缺省值为0。",
    )
    is_defo_image_enabled: Literal[0, 1] = Field(
        alias="DefoImageEn", description="形变图像是否有效。0: 无效; 1: 有效。"
    )
    defo_image_decimation: int = Field(
        default=1,
        alias="DefoImageDec",
        ge=0,
        description="形变图像帧抽取。0: 无效; 1及以上: 抽取比。缺省值为1。",
    )
    is_mission_id_enabled: Literal[0, 1] = Field(
        alias="MissionIDSwitch",
        description="上位机任务ID是否启用。0：雷达自己生成任务ID；1：使用上位机设置任务ID。",
    )
    mission_id: int = Field(
        alias="MissionID", description="任务ID，用于区分不同的扫描任务。"
    )

    # =================================
    # 其他参数 (Other Parameters)
    # =================================
    antenna_azimuth_beam_width_degrees: int = Field(
        alias="AntBWAz", description="天线方位波束角，单位为度。"
    )
    antenna_vertical_steering_angle: int = Field(
        alias="AntSteerVt",
        description="天线舵机俯仰。单位为0.1°，实际角度乘以10后取整。例如，1度表示为10。",
    )
    radar_longitude: float = Field(
        alias="RadarLon",
        ge=-180.0,
        le=180.0,
        description="雷达位置经度，格式为 dd.dddddd°。",
    )
    radar_latitude: float = Field(
        alias="RadarLat",
        ge=-90.0,
        le=90.0,
        description="雷达位置纬度，格式为 dd.dddddd°。",
    )
    radar_height_meters: float = Field(
        alias="RadarHei", description="雷达位置高度，单位为米。"
    )
    radar_orientation_degrees: float = Field(
        alias="RadarOri",
        ge=0,
        lt=360,
        description="雷达转台零点朝向，相对于正北方向顺时针，格式为 dd.dd°。",
    )
    radar_arm_length_meters: float = Field(
        alias="RadarArmLen",
        ge=0,
        description="雷达转臂长度，单位为米。指转轴到天线相位中心的距离。",
    )
    filter_type: float = Field(
        alias="FilterType",
        ge=0,
        le=10,
        description="滤波控制。0: 不滤波; 1-10: 值越大滤波越强。",
    )
    localization_mode: Literal[0, 1] = Field(
        alias="LocaltionType",  # 保持表格中的原始别名(LocaltionType)
        description="定位模式。0: 手动定位; 1: 自动定位。",
    )

    @model_validator(mode="after")
    def check_range_min_max(self) -> "SceneParameter":
        """验证回波最远距离必须大于或等于最近距离"""
        if self.range_min_meters > self.range_max_meters:
            raise ValueError(
                "回波最远距离 (RangeMax) 必须大于或等于回波最近距离 (RangeMin)"
            )
        return self

    def pack_to_bytes(self) -> bytes:
        format_string = "<H 4f 2I 12x 7H I 4x 2H 3d 3f H 4x"
        field_order = [
            "rotation_rate",
            "rotation_angle_start_degrees",
            "rotation_angle_end_degrees",
            "range_min_meters",
            "range_max_meters",
            "radar_mode",
            "point_selection_threshold",
            "start_dwell_time_seconds",
            "end_dwell_time_seconds",
            "is_scatter_image_enabled",
            "scatter_image_decimation",
            "is_defo_image_enabled",
            "defo_image_decimation",
            "is_mission_id_enabled",
            "mission_id",
            "antenna_azimuth_beam_width_degrees",
            "antenna_vertical_steering_angle",
            "radar_longitude",
            "radar_latitude",
            "radar_height_meters",
            "radar_orientation_degrees",
            "radar_arm_length_meters",
            "filter_type",
            "localization_mode",
        ]

        # 关键代码在这里
        values = [getattr(self, key) for key in field_order]

        return struct.pack(format_string, *values)

    @staticmethod
    def unpack_from_bytes(data: bytes) -> "SceneParameter":
        format_string = "<H 4f 2I 12x 7H I 4x 2H 3d 3f H 4x"
        field_order = [
            "rotation_rate",
            "rotation_angle_start_degrees",
            "rotation_angle_end_degrees",
            "range_min_meters",
            "range_max_meters",
            "radar_mode",
            "point_selection_threshold",
            "start_dwell_time_seconds",
            "end_dwell_time_seconds",
            "is_scatter_image_enabled",
            "scatter_image_decimation",
            "is_defo_image_enabled",
            "defo_image_decimation",
            "is_mission_id_enabled",
            "mission_id",
            "antenna_azimuth_beam_width_degrees",
            "antenna_vertical_steering_angle",
            "radar_longitude",
            "radar_latitude",
            "radar_height_meters",
            "radar_orientation_degrees",
            "radar_arm_length_meters",
            "filter_type",
            "localization_mode",
        ]
        values = struct.unpack(format_string, data)
        return SceneParameter.model_validate(dict(zip(field_order, values)))


# ==============================================================================
# 单例管理器
# ==============================================================================
class RadarManager:
    _instance = None
    _lock = threading.Lock()

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance

    def __init__(self) -> None:
        self.ArcSAR_id_map: Dict[str, "ArcSAR"] = {}
        self._map_lock: threading.RLock = threading.RLock()

    def register_radar(self, radar_id: str, radar_instance: "ArcSAR"):
        with self._map_lock:
            self.ArcSAR_id_map[radar_id] = radar_instance
            logger.info(f"雷达 {radar_id} 已注册。")

    def unregister_radar(self, radar_id: str):
        with self._map_lock:
            if radar_id in self.ArcSAR_id_map:
                del self.ArcSAR_id_map[radar_id]
                logger.info(f"雷达 {radar_id} 已注销。")

    def get_radar(self, radar_id: str) -> Optional["ArcSAR"]:
        with self._map_lock:
            return self.ArcSAR_id_map.get(radar_id)

    def get_radar_ids(self) -> List[str]:
        with self._map_lock:
            return list(self.ArcSAR_id_map.keys())


manager = RadarManager.get_instance()


# ==============================================================================
# CPU密集型辅助函数
# ==============================================================================
def polar2cart(
    img: np.ndarray,
    rng_min: float,
    rng_res: float,
    rng_num: int,
    ang_min: float,
    ang_res: float,
    ang_num: int,
    file_path: str,
):
    try:
        pid = os.getpid()
        logger.info(f"子进程 {pid}: 开始图像转换 -> {os.path.basename(file_path)}")
        ang = np.linspace(ang_min, ang_min + ang_res * (ang_num - 1), ang_num)
        rng = np.linspace(rng_min, rng_min + rng_res * (rng_num - 1), rng_num)
        rr, aa = np.meshgrid(rng, ang)
        x = rr * np.cos(np.deg2rad(aa))
        y = rr * np.sin(np.deg2rad(aa))
        x_cart = np.linspace(x.min(), x.max(), rng_num)
        y_cart = np.linspace(y.min(), y.max(), ang_num)
        xx_new, yy_new = np.meshgrid(x_cart, y_cart)
        points = np.vstack((x.ravel(), y.ravel())).T
        values = img.ravel()
        img_cart = griddata(
            points, values, (xx_new, yy_new), method="linear", fill_value=0
        )

        # 避免log(0)警告
        non_zero_min = np.min(img_cart[img_cart > 0]) if (img_cart > 0).any() else 1e-9
        with np.errstate(divide="ignore"):
            img_log = 20 * np.log10(
                np.where(img_cart <= 0, non_zero_min, img_cart) / np.max(img_cart)
            )

        # 处理可能出现的-inf
        if np.isneginf(img_log).any():
            finite_min = (
                np.min(img_log[np.isfinite(img_log)])
                if np.isfinite(img_log).any()
                else -100
            )
            img_log[np.isneginf(img_log)] = finite_min

        plt.figure(figsize=(10, 10))
        plt.axis("off")
        min_val = np.min(img_log)
        alpha = np.ones_like(img_log)
        alpha[img_log == min_val] = 0
        plt.imshow(img_log, cmap="jet", aspect="auto", alpha=alpha)
        plt.savefig(file_path, bbox_inches="tight", pad_inches=0, transparent=True)
        plt.close()
        logger.info(f"子进程 {pid}: 成功保存图像 {os.path.basename(file_path)}")
    except Exception as e:
        logger.error(f"子进程 {os.getpid()}: polar2cart 中发生错误: {e}", exc_info=True)


# ==============================================================================
# ASYNCIO 重构: ArcSAR 业务逻辑类
# ==============================================================================
class ArcSAR:
    HEADER_FORMAT = "<H I B B B I B 10x I"
    HEADER_SIZE = struct.calcsize(HEADER_FORMAT)

    def __init__(
        self,
        reader: asyncio.StreamReader,
        writer: asyncio.StreamWriter,
        loop: asyncio.AbstractEventLoop,
        process_pool: ProcessPoolExecutor,
        thread_pool: ThreadPoolExecutor,
    ):
        self.id: str = ""
        self.reader = reader
        self.writer = writer
        self.address = writer.get_extra_info("peername")
        self.loop = loop
        self.process_pool = process_pool
        self.thread_pool = thread_pool
        self.counter: int = 0
        self.connection_time = time.time()
        self.command_queue: AsyncQueue = AsyncQueue()
        self.pending_responses: Dict[int, Tuple[AsyncEvent, Dict[str, Any]]] = {}

    async def recv_data(self) -> Optional[Tuple[bytes, bytes]]:
        try:
            header_buffer = await self.reader.readexactly(self.HEADER_SIZE)
            *_, data_len = struct.unpack(self.HEADER_FORMAT, header_buffer)
            payload_buffer = (
                await self.reader.readexactly(data_len) if data_len > 0 else b""
            )
            return header_buffer, payload_buffer
        except (asyncio.IncompleteReadError, ConnectionResetError):
            return None

    async def handle_radar_report(self, header_data: bytes, payload_data: bytes):
        try:
            (head, radar_id, command, extend, sign, con_int, state_code, data_len) = (
                struct.unpack(self.HEADER_FORMAT, header_data)
            )
        except struct.error as e:
            logger.error(
                f"[雷达{self.id or '未知'}] 无法从{self.address}处正确解包头部: {e}"
            )
            return

        if not self.id:
            self.id = f"{radar_id:08X}"
            logger.info(f"[雷达{self.id}] 来自{self.address}的雷达ID为: {self.id}")
            manager.register_radar(self.id, self)
            await self.register_or_update_online_status()

        dispatch_map = {
            UploadDataType.INFO.value: self.update_radar_imformation,
            UploadDataType.IMAGE.value: self.upload_image_data,
            UploadDataType.DEFO.value: self.upload_defo_or_confi_data,
            UploadDataType.CONFIDENCE.value: self.upload_defo_or_confi_data,
            UploadDataType.MOVINGTARGET.value: self.upload_moving_target_data,
            UploadDataType.LOG.value: self.upload_log_file,
            UploadDataType.TIME.value: self.update_radar_time,
        }
        handler = dispatch_map.get(extend)
        if not handler:
            logger.warning(f"未知的上报扩展码: {extend}")
            return

        respond_data_bytes = await handler(payload_data)

        respond_data_len = len(respond_data_bytes)
        response_header = struct.pack(
            self.HEADER_FORMAT,
            CommandHeader.PLATFORM.value,
            radar_id,
            command,
            extend,
            0x01,
            con_int,
            0x00,
            respond_data_len,
        )
        if not self.writer.is_closing():
            try:
                self.writer.write(response_header + respond_data_bytes)
                await self.writer.drain()
            except ConnectionResetError:
                logger.warning(f"尝试向已关闭的连接 {self.address} 发送响应时失败。")

    async def register_or_update_online_status(self) -> bool:
        try:
            radar_doc = await radar_collection.find_one({"ID": self.id})
            if not radar_doc:
                logger.info(f"[雷达{self.id}] 新雷达，初始化数据库...")
                await radar_collection.insert_one(
                    {
                        "ID": self.id,
                        "name": f"Radar{self.id}",
                        "is_online": 1,
                        "is_work": 0,
                        "mission_ID": [],
                        "radar_coordinates": [],
                        "scene": None,
                    }
                )

                def _blocking_setup():
                    radar_file_path = os.path.join(BASE_FILE_PATH, self.id)
                    for subdir in [
                        "algorithm_file",
                        "log_file",
                        "work_data",
                        "radar_file",
                    ]:
                        os.makedirs(
                            os.path.join(radar_file_path, subdir), exist_ok=True
                        )

                    excel_path = os.path.join(BASE_FILE_PATH, "data.xlsx")
                    if not os.path.exists(excel_path):
                        logger.warning(
                            f"Excel配置文件未找到: {excel_path}。跳过数据导入。"
                        )
                        return

                    excel_sheets = {
                        "雷达信息": "radar_information",
                        "场景参数": "scene_parameter",
                        "平台命令": "platform_command",
                    }
                    sync_client = MongoClient(MONGO_URI)
                    db_this_radar_blocking = sync_client[self.id]
                    try:
                        for sheet_name, collection_name in excel_sheets.items():
                            try:
                                df = pd.read_excel(excel_path, sheet_name=sheet_name)
                                if not df.empty:
                                    db_this_radar_blocking[collection_name].insert_many(
                                        df.to_dict("records")
                                    )
                            except Exception as ex_sheet:
                                logger.error(
                                    f"处理Excel工作表 '{sheet_name}' 失败: {ex_sheet}"
                                )
                    finally:
                        sync_client.close()

                await self.loop.run_in_executor(self.thread_pool, _blocking_setup)
                logger.info(f"[雷达{self.id}] 注册成功")
                return True
            else:
                logger.info(f"[雷达{self.id}] 已注册雷达，更新在线状态")
                await radar_collection.update_one(
                    {"ID": self.id}, {"$set": {"is_online": 1}}
                )
                return True
        except Exception as e:
            logger.error(
                f"[雷达{self.id}] 注册或更新状态时发生严重错误: {e}", exc_info=True
            )
            return False

    async def update_radar_imformation(self, payload_data: bytes) -> bytes:
        db_this_radar = client[self.id]
        radar_info_coll = db_this_radar["radar_information"]
        type_parsers = {
            "charchar": lambda p, s: ".".join(
                map(str, struct.unpack(f"<{s}B", payload_data[p : p + s]))
            ),
            "char": lambda p, s: chr(struct.unpack("<B", payload_data[p : p + s])[0]),
            "float32": lambda p, s: struct.unpack("<f", payload_data[p : p + s])[0],
            "float64": lambda p, s: struct.unpack("<d", payload_data[p : p + s])[0],
            "int16": lambda p, s: struct.unpack("<h", payload_data[p : p + s])[0],
            "int32": lambda p, s: struct.unpack("<I", payload_data[p : p + s])[0],
        }
        try:
            async for doc in radar_info_coll.find():
                try:
                    parsed_data = type_parsers[doc["data_type"]](
                        doc["start_byte_position"], doc["all_byte"]
                    )
                    await radar_info_coll.update_one(
                        {"_id": doc["_id"]}, {"$set": {"data": parsed_data}}
                    )
                except Exception as e:
                    logger.error(f"[雷达{self.id}] 解析或更新雷达信息时出错: {e}")
        except Exception as e:
            logger.error(f"[雷达{self.id}] 无法从数据库获取雷达信息文档: {e}")
        return b""

    async def upload_image_data(self, payload_data: bytes) -> bytes:
        try:
            meta_format = "<I16s2IfIffIfIIf"
            meta_size = struct.calcsize(meta_format)
            (
                seq,
                md5,
                time_stamp,
                mission_id,
                rng_res,
                rng_num,
                rng_min,
                ang_res,
                ang_num,
                ang_min,
                data_type,
                sca_im_size,
                img_max_amp,
            ) = struct.unpack(meta_format, payload_data[:meta_size])

            md5_calc = hashlib.md5(payload_data[struct.calcsize("<I16s") :]).hexdigest()
            if md5.hex() != md5_calc:
                logger.error(f"散射图像数据MD5校验失败")
                return b""

            M, N = int(rng_num), int(ang_num)
            img_bytes = cast(bytes, snappy.uncompress(payload_data[meta_size:]))
            mag_data = (
                np.frombuffer(img_bytes[: 2 * M * N], dtype=np.float16)
                .reshape(N, M)
                .astype(np.float32)
            )
            phase_data = (
                np.frombuffer(img_bytes[2 * M * N :], dtype=np.float16)
                .reshape(N, M)
                .astype(np.float32)
            )

            base_path = os.path.join(
                BASE_FILE_PATH, self.id, "work_data", str(mission_id), "image_data"
            )
            paths = {
                "polar": os.path.join(base_path, f"polar_{seq}.tiff"),
                "phase": os.path.join(base_path, f"phase_{seq}.tiff"),
                "mag": os.path.join(base_path, f"magnitude_{seq}.tiff"),
                "cart": os.path.join(base_path, f"cart_{seq}.png"),
            }

            r_doc = await radar_collection.find_one({"ID": self.id})
            m_list = r_doc.get("mission_ID", []) if r_doc else []
            m_details = next(
                (m for m in m_list if m.get("ID") == str(mission_id)), None
            )
            meta = {
                "description": "幅度图与相位图",
                "theta0": ang_min,
                "dtheta": ang_res,
                "r0": rng_min,
                "dr": rng_res,
                "coordinates": m_details.get("coordinates", []) if m_details else [],
            }

            def _blocking_io():
                os.makedirs(base_path, exist_ok=True)
                imwrite(
                    paths["polar"],
                    np.stack([mag_data, phase_data], axis=0),
                    metadata=meta,
                )
                imwrite(paths["phase"], phase_data, metadata=meta)
                imwrite(paths["mag"], mag_data, metadata=meta)

            await self.loop.run_in_executor(self.thread_pool, _blocking_io)
            await self.loop.run_in_executor(
                self.process_pool,
                polar2cart,
                mag_data,
                rng_min,
                rng_res,
                M,
                ang_min,
                ang_res,
                N,
                paths["cart"],
            )

            db_radar = client[self.id]
            img_coll = db_radar[f"img_data_{mission_id}"]
            img_doc = {
                "扫描序号": seq,
                "时间戳": time_stamp,
                "任务ID": mission_id,
                "距离像素间隔": rng_res,
                "距离像素点数量": rng_num,
                "最小距离": rng_min,
                "角度像素间隔": ang_res,
                "角度像素点数量": ang_num,
                "最小角度": ang_min,
                "数据类型": data_type,
                "最大幅值": img_max_amp,
                "road_polar": paths["polar"],
                "road_cart": paths["cart"],
                "road_polar_phase": paths["phase"],
                "road_polar_magntiude": paths["mag"],
            }
            await img_coll.insert_one(img_doc)
        except Exception as e:
            logger.error(f"[雷达{self.id}] 处理图像数据失败: {e}", exc_info=True)
        return b""

    async def upload_defo_or_confi_data(self, payload_data: bytes) -> bytes:
        try:
            meta_format = "<I16s2IfIffIfII"
            meta_size = struct.calcsize(meta_format)
            (
                seq,
                md5,
                time_stamp,
                mission_id,
                rng_res,
                rng_num,
                rng_min,
                ang_res,
                ang_num,
                ang_min,
                data_type,
                im_size,
            ) = struct.unpack(meta_format, payload_data[:meta_size])

            if data_type == 20:
                img_type_en, img_type_cn = "deformation", "形变"
            elif data_type == 30:
                img_type_en, img_type_cn = "Confidence", "置信度"
            else:
                logger.error(f"未知的数据类型: {data_type}")
                return b""

            md5_calc = hashlib.md5(payload_data[struct.calcsize("<I16s") :]).hexdigest()
            if md5.hex() != md5_calc:
                logger.error(f"{img_type_cn}图像数据MD5校验失败")
                return b""

            M, N = int(rng_num), int(ang_num)
            img_bytes = snappy.uncompress(payload_data[meta_size:])
            img_data = (
                np.frombuffer(cast(bytes, img_bytes), dtype=np.float16)
                .reshape(N, M)
                .astype(np.float32)
            )

            base_path = os.path.join(
                BASE_FILE_PATH,
                self.id,
                "work_data",
                str(mission_id),
                f"{img_type_en}_data",
            )
            file_path = os.path.join(base_path, f"{img_type_en}_img_{seq}.tiff")

            def _blocking_io():
                os.makedirs(base_path, exist_ok=True)
                imwrite(
                    file_path, img_data, metadata={"description": f"{img_type_cn}图像"}
                )

            await self.loop.run_in_executor(self.thread_pool, _blocking_io)

            db_radar = client[self.id]
            img_coll = db_radar[f"{img_type_en.lower()}_data_{mission_id}"]
            img_doc = {
                "扫描序号": seq,
                "时间戳": time_stamp,
                "任务ID": mission_id,
                "距离像素间隔": rng_res,
                "距离像素点数量": rng_num,
                "最小距离": rng_min,
                "角度像素间隔": ang_res,
                "角度像素点数量": ang_num,
                "最小角度": ang_min,
                "数据类型": data_type,
                "图像大小": im_size,
                "road_path": file_path,
            }
            await img_coll.insert_one(img_doc)
        except Exception as e:
            logger.error(f"[雷达{self.id}] 处理形变/置信度数据失败: {e}", exc_info=True)
        return b""

    async def upload_moving_target_data(self, payload_data: bytes) -> bytes:
        try:
            meta_format = "<I16s2IfIffIfIII"
            meta_size = struct.calcsize(meta_format)
            (
                seq,
                md5,
                time_stamp,
                mission_id,
                rng_res,
                rng_num,
                rng_min,
                ang_res,
                ang_num,
                ang_min,
                data_type,
                mt_num,
                mt_data_size,
            ) = struct.unpack(meta_format, payload_data[:meta_size])

            md5_calc = hashlib.md5(payload_data[struct.calcsize("<I16s") :]).hexdigest()
            if md5.hex() != md5_calc:
                logger.error("动目标数据MD5校验失败")
                return b""

            mt_bytes = payload_data[meta_size : meta_size + mt_data_size]
            all_target = []
            for i in range(mt_num):
                target_data = struct.unpack("<4f", mt_bytes[i * 16 : (i + 1) * 16])
                all_target.append(
                    {
                        "目标编号": target_data[0],
                        "目标角度": target_data[1],
                        "目标距离": target_data[2],
                        "目标速度": target_data[3],
                    }
                )

            db_radar = client[self.id]
            mt_coll = db_radar[f"move_target_data_{mission_id}"]
            mt_doc = {
                "扫描序号": seq,
                "时间戳": time_stamp,
                "任务ID": mission_id,
                "动目标个数": mt_num,
                "目标信息": all_target,
            }
            await mt_coll.insert_one(mt_doc)
        except Exception as e:
            logger.error(f"[雷达{self.id}] 处理动目标数据失败: {e}", exc_info=True)
        return b""

    async def upload_log_file(self, payload_data: bytes) -> bytes:
        try:
            name_str = payload_data[:128].decode("ascii").split("\x00", 1)[0]
            log_path = os.path.join(BASE_FILE_PATH, self.id, "log_file", name_str)

            def _blocking_write():
                os.makedirs(os.path.dirname(log_path), exist_ok=True)
                with open(log_path, "ab") as f:
                    f.write(payload_data[128:])

            await self.loop.run_in_executor(self.thread_pool, _blocking_write)
        except Exception as e:
            logger.error(f"[雷达{self.id}] 保存日志文件失败: {e}", exc_info=True)
        return b""

    async def update_radar_time(self, _) -> bytes:
        return struct.pack("<I", int(time.time()))

    async def _send_command_and_wait(
        self,
        command: int,
        extend: int,
        info: str,
        payload: bytes = b"",
        timeout: int = 10,
    ) -> Tuple[str, Optional[Dict[str, Any]]]:
        if not self.id:
            logger.error("无法发送指令：雷达ID未知")
            return "ID未知", None
        self.counter += 1
        counter = self.counter
        response_event = AsyncEvent()
        response_data: Dict[str, Any] = {}
        self.pending_responses[counter] = (response_event, response_data)
        header = struct.pack(
            self.HEADER_FORMAT,
            CommandHeader.PLATFORM.value,
            int(self.id, 16),
            command,
            extend,
            0x00,
            counter,
            0x00,
            len(payload),
        )
        await self.command_queue.put(header + payload)
        try:
            await asyncio.wait_for(response_event.wait(), timeout=timeout)
        except asyncio.TimeoutError:
            self.pending_responses.pop(counter, None)
            logger.error(f"等待指令 {info} 响应超时")
            return "等待响应超时", None
        state_code = cast(int, response_data.get("state_code"))
        state = {
            0x00: "设置正确",
            0x01: "参数错误",
            0x02: "数据长度错误",
            0x03: "重发",
            0x04: "雷达正在加载参数",
            0x05: "处于工作状态",
        }.get(state_code, "未知错误")
        return state, response_data

    async def set_scene_parameter(self, parameters: Dict[str, Any]) -> str:
        try:
            validated_params = SceneParameter.model_validate(parameters)
            payload = validated_params.pack_to_bytes()
        except ValidationError as e:
            logger.error(f"场景参数校验失败: {e}")
            return "error"
        state, _ = await self._send_command_and_wait(
            CommandCode.SETPARAMETER.value,
            SetParamter.SCENE.value,
            "设置场景参数",
            payload,
        )
        return "success" if state == "设置正确" else "error"

    async def start_work(self) -> str:
        state, _ = await self._send_command_and_wait(
            CommandCode.WORKCONTROL.value, WorkCommand.START.value, "雷达开机"
        )
        if state == "设置正确":
            await radar_collection.update_one({"ID": self.id}, {"$set": {"is_work": 1}})
            return "success"
        return "error"

    async def stop_work(self) -> str:
        state, _ = await self._send_command_and_wait(
            CommandCode.WORKCONTROL.value, WorkCommand.STOP.value, "雷达关机"
        )
        if state == "设置正确":
            await radar_collection.update_one({"ID": self.id}, {"$set": {"is_work": 0}})
            return "success"
        return "error"

    async def radar_disconnect(self):
        if self.id:
            logger.info(f"雷达{self.id}断开连接，清理资源...")
            await radar_collection.update_one(
                {"ID": self.id}, {"$set": {"is_online": 0}}
            )
            manager.unregister_radar(self.id)
            [evt.set() for _, (evt, _) in self.pending_responses.items()]
            self.pending_responses.clear()
            if self.writer.can_write_eof():
                try:
                    self.writer.close()
                except:
                    pass
            await self.writer.wait_closed()
        else:
            logger.info(f"未识别的雷达 {self.address} 断开连接。")


# ==============================================================================
# ASYNCIO 重构: 客户端处理器
# ==============================================================================
async def handle_client(
    reader: asyncio.StreamReader,
    writer: asyncio.StreamWriter,
    loop: asyncio.AbstractEventLoop,
    process_pool: ProcessPoolExecutor,
    thread_pool: ThreadPoolExecutor,
):
    radar_client = ArcSAR(reader, writer, loop, process_pool, thread_pool)
    addr = radar_client.address
    logger.info(f"接受了来自 {addr} 的新连接")

    async def command_sender():
        try:
            while not writer.is_closing():
                command_to_send = await radar_client.command_queue.get()
                writer.write(command_to_send)
                await writer.drain()
        except (asyncio.CancelledError, ConnectionResetError):
            pass
        finally:
            while not radar_client.command_queue.empty():
                radar_client.command_queue.get_nowait()
                radar_client.command_queue.task_done()

    async def data_receiver():
        try:
            while not writer.is_closing():
                package = await radar_client.recv_data()
                if not package:
                    break
                header_data, payload_data = package
                try:
                    *_, sign, _, con_int, state_code, _ = struct.unpack(
                        ArcSAR.HEADER_FORMAT, header_data
                    )
                    if sign == 0x01:  # 响应包
                        if con_int in radar_client.pending_responses:
                            evt, container = radar_client.pending_responses.pop(con_int)
                            container.update(
                                {"state_code": state_code, "data": payload_data}
                            )
                            evt.set()
                    else:  # 上报包
                        asyncio.create_task(
                            radar_client.handle_radar_report(header_data, payload_data)
                        )
                except struct.error:
                    logger.error(f"接收到损坏的头部数据，忽略...")
        except (asyncio.CancelledError, ConnectionResetError):
            pass
        except Exception as e:
            logger.error(f"处理来自 {addr} 的数据时发生错误: {e}", exc_info=True)

    sender_task = asyncio.create_task(command_sender())
    receiver_task = asyncio.create_task(data_receiver())
    await asyncio.gather(receiver_task)
    sender_task.cancel()
    await asyncio.gather(sender_task, return_exceptions=True)
    await radar_client.radar_disconnect()
    logger.info(f"与 {addr} 的连接已完全关闭。")


# ==============================================================================
# ASYNCIO 重构: 主服务器入口
# ==============================================================================
async def main_server() -> None:
    process_pool = ProcessPoolExecutor(max_workers=os.cpu_count() or 4)
    thread_pool = ThreadPoolExecutor(max_workers=50, thread_name_prefix="IO_Worker")
    loop = asyncio.get_running_loop()
    server = await asyncio.start_server(
        lambda r, w: handle_client(r, w, loop, process_pool, thread_pool), HOST, PORT
    )
    addrs = ", ".join(str(sock.getsockname()) for sock in server.sockets)
    logger.info(f"服务器已在 {addrs} 启动，使用 asyncio + motor 模型...")
    async with server:
        await server.serve_forever()
    logger.info("正在关闭执行器池...")
    process_pool.shutdown(wait=True)
    thread_pool.shutdown(wait=True)
    logger.info("执行器池已关闭。")


# ==============================================================================
# 模拟器辅助函数 (完整版)
# ==============================================================================
def create_fake_scatter_image_packet(
    radar_id: int, sequence_number: int, mission_id: int
) -> bytes:
    rng_res, rng_num, rng_min = 0.5, 50, 10.0
    ang_res, ang_num, ang_min = 0.1, 50, 0.0
    data_type, img_max_amp = 10, 1.0
    M, N = int(rng_num), int(ang_num)
    mag_data = np.random.rand(N, M).astype(np.float16)
    phase_data = (np.random.rand(N, M) * 2 * np.pi - np.pi).astype(np.float16)
    img_data_uncompressed = np.concatenate(
        [mag_data.ravel(), phase_data.ravel()]
    ).tobytes()
    img_data_compressed = snappy.compress(img_data_uncompressed)
    sca_im_size = len(img_data_compressed)
    time_stamp = int(time.time())
    data_to_hash = (
        struct.pack(
            "<2IfIffIfIIf",
            time_stamp,
            mission_id,
            rng_res,
            rng_num,
            rng_min,
            ang_res,
            ang_num,
            ang_min,
            data_type,
            sca_im_size,
            img_max_amp,
        )
        + img_data_compressed
    )
    md5_hash = hashlib.md5(data_to_hash).digest()
    payload_header = struct.pack(
        "<I16s2IfIffIfIIf",
        sequence_number,
        md5_hash,
        time_stamp,
        mission_id,
        rng_res,
        rng_num,
        rng_min,
        ang_res,
        ang_num,
        ang_min,
        data_type,
        sca_im_size,
        img_max_amp,
    )
    payload = payload_header + img_data_compressed
    header = struct.pack(
        ArcSAR.HEADER_FORMAT,
        CommandHeader.RADAR.value,
        radar_id,
        CommandCode.UPLOADDATA.value,
        UploadDataType.IMAGE.value,
        0x00,
        sequence_number,
        0x00,
        len(payload),
    )
    return header


# ==============================================================================
# 模拟器代码 (完整版)
# ==============================================================================
def simulate_radar(host, port):
    FAKE_RADAR_ID_INT = 0x001005AD
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    state = {
        "is_working": False,
        "is_connected": False,
        "upload_interval": 3,
        "current_mission_id": 12345,
        "lock": threading.Lock(),
    }

    def _sender_thread():
        seq = 1
        while True:
            with state["lock"]:
                is_connected = state["is_connected"]
                is_working = state["is_working"]
                mission_id = state["current_mission_id"]
            if not is_connected:
                break
            if is_working:
                logger.info(f"[模拟器] >> 模拟发送第 {seq} 帧数据...")
                packet = create_fake_scatter_image_packet(
                    FAKE_RADAR_ID_INT, seq, mission_id
                )
                try:
                    sock.sendall(packet)
                    seq += 1
                except (BrokenPipeError, ConnectionResetError):
                    logger.error("[模拟器 Sender] 连接已断开")
                    with state["lock"]:
                        state["is_connected"] = False
                        break
            time.sleep(state["upload_interval"])

    try:
        sock.connect((host, port))
        logger.info(f"[模拟器] 已连接到服务器 {host}:{port}")
        with state["lock"]:
            state["is_connected"] = True
        sender = threading.Thread(target=_sender_thread, daemon=True, name="SimSender")
        sender.start()
        initial_packet = struct.pack(
            ArcSAR.HEADER_FORMAT,
            CommandHeader.RADAR.value,
            FAKE_RADAR_ID_INT,
            CommandCode.UPLOADDATA.value,
            UploadDataType.TIME.value,
            0,
            0,
            0,
            0,
        )
        sock.sendall(initial_packet)
        while True:
            header_data = sock.recv(ArcSAR.HEADER_SIZE)
            if not header_data:
                logger.info("[模拟器] 服务器关闭了连接。")
                break
            (head, radar_id, command, extend, sign, con_int, state_code, data_len) = (
                struct.unpack(ArcSAR.HEADER_FORMAT, header_data)
            )
            payload = sock.recv(data_len) if data_len > 0 else b""
            if sign == 0x01:  # 这是对我们指令的响应
                logger.info(
                    f"[模拟器] << 收到响应: cmd=0x{command:02x}, ext=0x{extend:02x}"
                )
                if command == CommandCode.WORKCONTROL.value:
                    with state["lock"]:
                        if extend == WorkCommand.START.value:
                            state["is_working"] = True
                            logger.info("[模拟器] 状态: 开始工作")
                        elif extend == WorkCommand.STOP.value:
                            state["is_working"] = False
                            logger.info("[模拟器] 状态: 停止工作")
                elif (
                    command == CommandCode.SETPARAMETER.value
                    and extend == SetParamter.SCENE.value
                ):
                    try:
                        params = SceneParameter.unpack_from_bytes(payload)
                        with state["lock"]:
                            state["current_mission_id"] = params.mission_id
                        logger.info(f"[模拟器] 任务ID更新为: {params.mission_id}")
                    except Exception as e:
                        logger.error(f"[模拟器] 解包场景参数失败: {e}")
                response_header = struct.pack(
                    ArcSAR.HEADER_FORMAT,
                    CommandHeader.RADAR.value,
                    FAKE_RADAR_ID_INT,
                    command,
                    extend,
                    0x01,
                    con_int,
                    0x00,
                    0,
                )
                sock.sendall(response_header)
    except Exception as e:
        logger.error(f"[模拟器] 发生错误: {e}", exc_info=True)
    finally:
        with state["lock"]:
            state["is_connected"] = False
        sock.close()
        logger.info("[模拟器] 连接已关闭。")


# ==============================================================================
# 主程序入口
# ==============================================================================
if __name__ == "__main__":
    excel_file = "data.xlsx"
    if not os.path.exists(excel_file):
        logger.warning(f"注意: '{excel_file}' 未找到。将使用空数据进行初始化。")
        logger.info(f"正在创建虚拟的 '{excel_file}' 文件...")
        with pd.ExcelWriter(excel_file, engine="openpyxl") as writer:
            pd.DataFrame(
                [
                    {
                        "name": "version",
                        "start_byte_position": 0,
                        "all_byte": 8,
                        "data_type": "char",
                        "data": "1.0.0",
                    }
                ]
            ).to_excel(writer, sheet_name="雷达信息", index=False)
            pd.DataFrame(
                [
                    {
                        "name": "RoRate",
                        "start_byte_position": 0,
                        "all_byte": 2,
                        "data_type": "int16",
                        "data": 0,
                    }
                ]
            ).to_excel(writer, sheet_name="场景参数", index=False)
            pd.DataFrame([{"name": "start", "code": 0}]).to_excel(
                writer, sheet_name="平台命令", index=False
            )

    if os.name == "nt":
        mp.set_start_method("spawn", force=True)
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    radar_simulator_thread = threading.Thread(
        target=simulate_radar,
        args=(HOST, PORT),
        name="RadarSimulatorThread",
        daemon=True,
    )
    radar_simulator_thread.start()

    try:
        asyncio.run(main_server())
    except KeyboardInterrupt:
        logger.info("服务器被用户中断，正在关闭...")
    finally:
        client.close()
        logger.info("服务器主程序已退出。")
