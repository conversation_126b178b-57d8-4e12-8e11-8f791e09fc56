<?xml version="1.0" encoding="UTF-8"?>
<module type="PYTHON_MODULE" version="4">
  <component name="FacetManager">
    <facet type="Python" name="Python facet">
      <configuration sdkName="Python 3.11 virtualenv at C:\Users\<USER>\Documents\Arcweb\backend\.venv" />
    </facet>
  </component>
  <component name="Flask">
    <option name="enabled" value="true" />
  </component>
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$" />
    <orderEntry type="jdk" jdkName="Python 3.11 virtualenv at C:\Users\<USER>\Documents\Arcweb\backend\.venv" jdkType="Python SDK" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Python 3.11 virtualenv at C:\Users\<USER>\Documents\Arcweb\backend\.venv interpreter library" level="application" />
  </component>
  <component name="TemplatesService">
    <option name="TEMPLATE_CONFIGURATION" value="Jinja2" />
  </component>
</module>