{"data_mtime": 1754494435, "dep_lines": [5, 6, 386, 1, 2, 3, 4, 5, 12, 15, 385, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 20, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["my_code.radar_code", "config.shared_config", "config.logging_config", "datetime", "bson", "flask", "flask_jwt_extended", "my_code", "validator", "services", "os", "builtins", "_frozen_importlib", "_typeshed", "abc", "bson.objectid", "flask.blueprints", "flask.json", "flask.sansio", "flask.sansio.blueprints", "flask.sansio.scaffold", "flask.wrappers", "flask_jwt_extended.view_decorators", "logging", "pymongo", "pymongo.common", "pymongo.synchronous", "pymongo.synchronous.mongo_client", "types", "typing", "typing_extensions", "validator.validator_framework", "watchfile", "werkzeug", "werkzeug.datastructures", "werkzeug.datastructures.headers", "werkzeug.sansio", "werkzeug.sansio.response", "werkzeug.wrappers", "werkzeug.wrappers.response", "wsgiref", "wsgiref.types"], "hash": "86fd910f9a023715ba038a8c74fb9a3321822871", "id": "web_code.data_analysis", "ignore_all": false, "interface_hash": "cddbcd3a431b78828b1f65dde78c0635e11283cc", "mtime": 1754494430, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\Arcweb\\backend\\my_code\\web_code\\data_analysis.py", "plugin_data": null, "size": 14415, "suppressed": [], "version_id": "1.15.0"}