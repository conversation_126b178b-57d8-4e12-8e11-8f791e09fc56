2025/08/01 18:30:42 [emerg] 5820#29880: unknown "prefix" variable
2025/08/01 18:31:52 [emerg] 30324#4532: invalid variable name in C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/conf/nginx.conf:51
2025/08/01 18:33:00 [error] 3192#28520: *1 directory index of "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/../terrain_data/sysu_sz/" is forbidden, client: 127.0.0.1, server: localhost, request: "GET /terrain_data/sysu_sz/ HTTP/1.1", host: "localhost:8000"
2025/08/01 18:33:00 [error] 3192#28520: *1 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost:8000", referrer: "http://localhost:8000/terrain_data/sysu_sz/"
2025/08/01 18:33:01 [error] 3192#28520: *1 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/01 18:34:06 [error] 3192#28520: *2 directory index of "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/../terrain_data/" is forbidden, client: 127.0.0.1, server: localhost, request: "GET /terrain_data/ HTTP/1.1", host: "localhost:8000"
2025/08/01 18:34:07 [error] 3192#28520: *2 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/01 18:34:12 [error] 3192#28520: *2 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/01 18:34:19 [error] 3192#28520: *2 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/01 18:39:14 [error] 3192#28520: *3 directory index of "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/../terrain_data/sysu_sz/" is forbidden, client: 127.0.0.1, server: localhost, request: "GET /terrain_data/sysu_sz/ HTTP/1.1", host: "localhost:8000"
2025/08/01 18:39:15 [error] 3192#28520: *3 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/01 18:39:46 [error] 3192#28520: *3 directory index of "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/../terrain_data/sysu_sz/" is forbidden, client: 127.0.0.1, server: localhost, request: "GET /terrain_data/sysu_sz/ HTTP/1.1", host: "localhost:8000"
2025/08/01 18:39:47 [error] 3192#28520: *3 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/01 18:39:47 [error] 3192#28520: *3 directory index of "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/../terrain_data/sysu_sz/" is forbidden, client: 127.0.0.1, server: localhost, request: "GET /terrain_data/sysu_sz/ HTTP/1.1", host: "localhost:8000"
2025/08/01 18:39:48 [error] 3192#28520: *3 directory index of "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/../terrain_data/sysu_sz/" is forbidden, client: 127.0.0.1, server: localhost, request: "GET /terrain_data/sysu_sz/ HTTP/1.1", host: "localhost:8000"
2025/08/01 18:39:48 [error] 3192#28520: *3 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/01 18:39:50 [error] 3192#28520: *3 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/01 18:40:33 [emerg] 1792#12116: no "events" section in configuration
2025/08/01 18:40:36 [emerg] 24768#29704: no "events" section in configuration
2025/08/01 18:41:33 [error] 27612#15368: *1 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/02 00:14:24 [error] 16476#35000: *175 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/02 00:14:25 [error] 16476#35000: *175 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/02 00:14:26 [error] 16476#35000: *175 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/02 00:14:28 [error] 16476#35000: *176 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost:8000", referrer: "http://localhost:8000/terrain_data/sysu_sz/layer.json"
2025/08/02 00:14:29 [error] 16476#35000: *175 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/06 17:23:35 [error] 368#6492: *17 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 17:41:08 [error] 368#6492: *25 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 17:41:11 [error] 368#6492: *27 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 18:29:14 [error] 368#6492: *107 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 18:29:17 [error] 368#6492: *109 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 18:29:20 [error] 368#6492: *111 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 19:47:31 [error] 368#6492: *132 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 19:47:33 [error] 368#6492: *134 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 19:50:13 [error] 368#6492: *143 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 19:50:52 [error] 368#6492: *145 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
