#!/usr/bin/env python3
"""
测试新的分层日志架构
验证各个模块的日志器是否正常工作
"""

from config.logging_config import (
    setup_logging,
    configure_module_loggers,
    get_api_logger,
    get_radar_logger,
    get_server_logger,
    get_simulator_logger,
    get_client_logger,
    get_worker_logger
)

def test_logging_system():
    """测试日志系统"""
    print("=== 测试分层日志架构 ===")
    
    # 初始化日志配置
    setup_logging()
    configure_module_loggers()
    
    # 获取各模块日志器
    api_logger = get_api_logger()
    radar_logger = get_radar_logger()
    server_logger = get_server_logger()
    simulator_logger = get_simulator_logger()
    client_logger = get_client_logger()
    worker_logger = get_worker_logger()
    
    print("日志配置初始化完成，开始测试各模块日志器...")
    
    # 测试API模块日志
    api_logger.info("[API] 用户登录请求")
    api_logger.debug("[API] 验证用户凭据")
    api_logger.warning("[API] 用户权限不足")
    
    # 测试雷达核心模块日志
    radar_logger.info("[雷达核心] 开始数据处理")
    radar_logger.debug("[雷达核心] 解析雷达数据包")
    radar_logger.error("[雷达核心] 数据包校验失败")
    
    # 测试服务器模块日志
    server_logger.info("[雷达服务器] 服务器启动，监听端口1030")
    server_logger.info("[雷达服务器] 新连接建立")
    server_logger.warning("[雷达服务器] 连接异常")
    
    # 测试模拟器模块日志
    simulator_logger.info("[模拟器] 开始发送散射图像数据")
    simulator_logger.debug("[模拟器] 构建响应载荷")
    simulator_logger.error("[模拟器] 连接被拒绝")
    
    # 测试客户端模块日志
    client_logger.info("[雷达客户端] 连接到服务器")
    client_logger.debug("[雷达客户端] 发送心跳包")
    client_logger.warning("[雷达客户端] 响应超时")
    
    # 测试Worker模块日志
    worker_logger.info("[Worker] 开始处理数据包")
    worker_logger.debug("[Worker] 数据包处理完成")
    worker_logger.error("[Worker] 处理数据包时发生错误")
    
    print("=== 日志测试完成 ===")
    print("请检查 log/radar.log 文件，确认各模块日志都已正确记录")
    print("日志格式应为: 时间戳 - 模块名 - 级别 - 消息")

if __name__ == "__main__":
    test_logging_system()
