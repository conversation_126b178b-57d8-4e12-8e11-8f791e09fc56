# def find_one_and_update(
#         self, query: Document, update: Document, upsert: bool = False
#     ) -> QueryResult[Document]:
#         @safe
#         def _do_update() -> Optional[Document]:
#             return self.collection.find_one_and_update(
#                 query, update, upsert=upsert, return_document=ReturnDocument.AFTER
#             )

#         result: Result[Optional[Document], Exception] = _do_update()

#         final_result: Result[Document, ValidationError] = result.alt(
#             self._handle_db_error
#         ).bind(
#             lambda doc: (
#                 Success(doc)
#                 if doc is not None
#                 else Failure(
#                     ValidationError(
#                         "error", "未找到要更新的记录", "RECORD_NOT_FOUND", None, 404
#                     )
#                 )
#             )
#         )

#         return QueryResult(final_result)
