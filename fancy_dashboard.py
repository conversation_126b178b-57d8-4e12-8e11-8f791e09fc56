import time
import logging
import logging.handlers
from multiprocessing import Process, Manager
from queue import Empty
from collections import deque
from datetime import datetime

# 新增导入
from rich.table import Table
from rich.console import Console
from rich.layout import Layout
from rich.live import Live
from rich.panel import Panel
from rich.text import Text

# --- 从您的项目中导入 ---
from config.logging_config import setup_logging
from app import main_app

# --- 配置 ---
MAX_LOG_LINES = 100
REFRESH_INTERVAL = 0.1

# --- 面板样式和分类规则 ---
PANELS_CONFIG = {
    "api": {
        "title": "[bold blue]API Logs (arcweb.api)[/bold blue]",
        "color": "blue",
        "logger_name": "arcweb.api",
    },
    "server": {
        "title": "[bold green]雷达服务器 Logs (arcweb.server)[/bold green]",
        "color": "green",
        "logger_name": "arcweb.server",
    },
    "simulator": {
        "title": "[bold magenta]模拟器 Logs (arcweb.simulator)[/bold magenta]",
        "color": "magenta",
        "logger_name": "arcweb.simulator",
    },
    "client": {
        "title": "[bold yellow]雷达客户端/雷达 Logs (arcweb.radar)[/bold yellow]",
        "color": "yellow",
        "logger_name": "arcweb.radar",
    },
    "worker": {
        "title": "[bold bright_cyan]Worker Logs (arcweb.worker)[/bold bright_cyan]",
        "color": "bright_cyan",
        "logger_name": "arcweb.worker",
    },
    "other": {
        "title": "[bold grey70]其他/未知来源 Logs[/bold grey70]",
        "color": "grey70",
        "logger_name": "",
    },
}

# --- UI 布局定义 ---
console = Console()
layout = Layout()
layout.split(Layout(name="header", size=3), Layout(ratio=1, name="main"))
layout["main"].split_row(Layout(name="left"), Layout(name="right"))
layout["left"].split_column(
    Layout(name="api"), Layout(name="simulator"), Layout(name="worker")
)
layout["right"].split_column(
    Layout(name="server"), Layout(name="client"), Layout(name="other")
)


def setup_queue_logging(log_queue):
    setup_logging()
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        if isinstance(handler, logging.StreamHandler):
            root_logger.removeHandler(handler)
    queue_handler = logging.handlers.QueueHandler(log_queue)
    root_logger.addHandler(queue_handler)
    root_logger.setLevel(logging.DEBUG)


def run_main_app(log_queue):
    setup_queue_logging(log_queue)
    main_app()


def categorize_log_record(record: logging.LogRecord) -> str:
    for panel_key, config in PANELS_CONFIG.items():
        if panel_key == "other":
            continue
        if record.name.startswith(config["logger_name"]):
            return panel_key
    return "other"


def format_log_record(record: logging.LogRecord) -> str:
    dt = datetime.fromtimestamp(record.created)
    asctime = dt.strftime("%Y-%m-%d %H:%M:%S,%f")[:-3]
    message = record.getMessage()
    if record.name == "werkzeug":
        try:
            http_part_start = message.find('"')
            if http_part_start != -1:
                http_part = message[http_part_start:]
                return f"{asctime} - {record.levelname} - {http_part}"
        except IndexError:
            pass
    if message.lstrip().startswith("[") and "]" in message:
        return f"{asctime} - {record.levelname} - {message}"
    return f"{asctime} - {record.name} - {record.levelname} - {message}"


def create_log_panel(category_name: str, logs: deque) -> Panel:
    config = PANELS_CONFIG[category_name]
    log_text = Text("\n".join(logs), style="white", no_wrap=True)
    if not logs:
        log_text = Text("暂无日志 ...", style="dim")
    log_text.highlight_regex(r"INFO", "bold green")
    log_text.highlight_regex(r"DEBUG", "bold cyan")
    log_text.highlight_regex(r"WARNING", "bold yellow")
    log_text.highlight_regex(r"ERROR", "bold red")
    log_text.highlight_regex(r"CRITICAL", "bold white on red")
    return Panel(
        log_text,
        title=config["title"],
        border_style=config["color"],
        title_align="left",
    )


def main():
    """主函数，启动应用和监控仪表盘"""
    logs = {name: deque(maxlen=MAX_LOG_LINES) for name in PANELS_CONFIG}

    with Manager() as manager:
        log_queue = manager.Queue()
        app_process = Process(target=run_main_app, args=(log_queue,))
        app_process.start()

        with Live(
            layout,
            console=console,
            screen=True,
            redirect_stderr=False,
            refresh_per_second=10,
        ) as live:
            try:
                # 初始化面板
                for name in logs:
                    layout[name].update(create_log_panel(name, logs[name]))

                while app_process.is_alive() or not log_queue.empty():
                    # --- 关键修改：动态生成 Header ---
                    header_table = Table.grid(expand=True)
                    header_table.add_column(justify="left", ratio=1)
                    header_table.add_column(justify="right")
                    now = datetime.now()
                    header_table.add_row(
                        "[bold]实时雷达系统日志监控 (按 Ctrl+C 退出)[/bold]",
                        f"[yellow]{now.strftime('%Y-%m-%d %H:%M:%S')}[/yellow]",
                    )
                    layout["header"].update(Panel(header_table, style="bold blue"))
                    # --- 修改结束 ---

                    updated_panels = set()

                    while not log_queue.empty():
                        try:
                            record = log_queue.get_nowait()
                            category = categorize_log_record(record)
                            display_line = format_log_record(record)
                            logs[category].appendleft(display_line)
                            updated_panels.add(category)
                        except Empty:
                            break

                    if updated_panels:
                        for panel_name in updated_panels:
                            panel = create_log_panel(panel_name, logs[panel_name])
                            layout[panel_name].update(panel)
                    else:
                        time.sleep(REFRESH_INTERVAL)

            except KeyboardInterrupt:
                console.print("[bold red]正在关闭监控和主应用程序...[/bold red]")
            finally:
                if app_process.is_alive():
                    app_process.terminate()
                    app_process.join()


if __name__ == "__main__":
    main()
