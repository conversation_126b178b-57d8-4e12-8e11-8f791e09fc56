import os
import time
import asyncio
import threading
import fnmatch
from typing import Set
from config.logging_config import get_simulator_logger

logger = get_simulator_logger()
# --- 依赖 ---
# 请确保已安装: pip install watchdog websockets
import websockets
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

# --- 工具类定义 ---


class FileChangeHandler(FileSystemEventHandler):
    """
    一个 Watchdog 事件处理器，当文件发生变化时，它会
    在一个 asyncio 事件循环中安全地调用一个异步回调函数。
    """

    def __init__(
        self, loop: asyncio.AbstractEventLoop, broadcast_callback, file_pattern: str
    ):
        self.loop = loop
        self.broadcast_callback = broadcast_callback
        self.file_pattern = file_pattern

    def on_modified(self, event):
        """当文件或目录被修改时调用。"""
        if not event.is_directory and fnmatch.fnmatch(
            event.src_path, self.file_pattern
        ):
            # 将异步的 broadcast_callback 任务提交到运行中的事件循环
            asyncio.run_coroutine_threadsafe(
                self.broadcast_callback(event.src_path), self.loop
            )


class FileWatcher:
    """
    一个可控的、支持环境变量热更新的文件监控工具。
    它会监控指定文件夹中的文件变化，并通过 WebSocket 进行广播。
    """

    def __init__(self):
        # 从环境变量加载初始配置
        self._load_config()

        # 内部状态
        self._thread = None
        self._observer = None
        self._stop_event = threading.Event()
        self._clients: Set[websockets.WebSocketServerProtocol] = set()

        logger.info(f"FileWatcher 初始化成功。配置如下:")
        self.print_config()

    def _load_config(self):
        """从环境变量加载配置，并提供合理的默认值。"""
        self.watch_folder = os.environ.get("WATCH_FOLDER", "log")
        self.file_pattern = os.environ.get("FILE_PATTERN", "*.log")
        self.websocket_host = os.environ.get("WEBSOCKET_HOST", "localhost")
        self.websocket_port = int(os.environ.get("WEBSOCKET_PORT", 8765))
        return (
            self.watch_folder,
            self.file_pattern,
            self.websocket_host,
            self.websocket_port,
        )

    def print_config(self):
        """打印当前配置。"""
        logger.info(f"  - 监控文件夹 (WATCH_FOLDER): {self.watch_folder}")
        logger.info(f"  - 文件模式 (FILE_PATTERN): {self.file_pattern}")
        logger.info(f"  - WebSocket Host: {self.websocket_host}:{self.websocket_port}")

    async def _websocket_handler(
        self, websocket: websockets.WebSocketServerProtocol, path: str
    ):
        """处理新的 WebSocket 连接。"""
        logger.info(f"客户端 {websocket.remote_address} 已连接。")
        self._clients.add(websocket)
        try:
            # 保持连接打开，直到客户端断开
            await websocket.wait_closed()
        finally:
            logger.info(f"客户端 {websocket.remote_address} 已断开。")
            self._clients.remove(websocket)

    async def _broadcast(self, message: str):
        """向所有连接的客户端广播消息。"""
        if self._clients:
            logger.info(
                f"检测到文件更新，向 {len(self._clients)} 个客户端广播: {message}"
            )
            # 使用 asyncio.gather 来并发地发送消息
            await asyncio.gather(*[client.send(message) for client in self._clients])

    def _start_observer(self, loop):
        """启动或重启 watchdog 监视器。"""
        # 如果已有监视器，先停止它
        if self._observer and self._observer.is_alive():
            self._observer.stop()
            self._observer.join()

        # 确保监控的文件夹存在
        if not os.path.isdir(self.watch_folder):
            logger.info(
                f"[警告] 文件夹 '{self.watch_folder}' 不存在。正在等待其被创建..."
            )
            # Watchdog 2.0+ 会自动处理文件夹的创建

        event_handler = FileChangeHandler(loop, self._broadcast, self.file_pattern)
        self._observer = Observer()
        # `recursive=True` 意味着也会监控子文件夹
        self._observer.schedule(event_handler, self.watch_folder, recursive=True)
        self._observer.start()
        logger.info(
            f"已开始在 '{self.watch_folder}' 中监控 '{self.file_pattern}' 文件。"
        )

    def _watch_loop(self):
        """在后台线程中运行的主循环。"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # 启动 WebSocket 服务器
        start_server = websockets.serve(
            self._websocket_handler, self.websocket_host, self.websocket_port
        )
        server = loop.run_until_complete(start_server)
        logger.info(
            f"WebSocket 服务器已在 ws://{self.websocket_host}:{self.websocket_port} 上启动。"
        )

        # 启动初始的 watchdog 监视器
        self._start_observer(loop)

        current_config = self._load_config()

        try:
            while not self._stop_event.is_set():
                # 运行 asyncio 事件循环一小段时间来处理 WebSocket 事件
                loop.run_until_complete(asyncio.sleep(1.0))

                # --- 热更新逻辑 ---
                new_config = self._load_config()
                # 只关心文件夹和模式的变化
                if (new_config[0], new_config[1]) != (
                    current_config[0],
                    current_config[1],
                ):
                    logger.info(f"\n[!] 检测到配置变化，正在热更新...")
                    self.print_config()
                    current_config = new_config
                    # 重启监视器以应用新路径和模式
                    self._start_observer(loop)
        finally:
            logger.info(f"正在关闭监控...")
            if self._observer and self._observer.is_alive():
                self._observer.stop()
                self._observer.join()
            server.close()
            loop.run_until_complete(server.wait_closed())
            loop.close()
            logger.info(f"监控已安全关闭。")

    def start(self):
        """启动后台监控线程。"""
        if self._thread is not None and self._thread.is_alive():
            logger.info(f"监控已经在运行中。")
            return

        self._stop_event.clear()
        self._thread = threading.Thread(target=self._watch_loop, daemon=True)
        self._thread.start()
        logger.info(f"监控线程已启动。")

    def stop(self):
        """停止后台监控线程。"""
        if self._thread is None or not self._thread.is_alive():
            logger.info(f"监控未在运行中。")
            return

        self._stop_event.set()
        self._thread.join()  # 等待线程完全退出
        self._thread = None
        logger.info(f"监控线程已停止。")


# --- 示例用法 ---
if __name__ == "__main__":
    logger.info(f"--- 文件监控工具示例 ---")

    # 设置初始环境变量 (在真实场景中，这通常由启动脚本或操作系统设置)
    os.environ["WATCH_FOLDER"] = "log"
    os.environ["FILE_PATTERN"] = "*.log"
    os.environ["WEBSOCKET_HOST"] = "localhost"
    os.environ["WEBSOCKET_PORT"] = "8765"

    # 1. 创建并启动监控器
    watcher = FileWatcher()
    watcher.start()

    logger.info(f"\n监控已在后台运行。现在您可以：")
    logger.info(
        f"1. 在另一个终端连接 WebSocket 客户端 (例如使用 'websocat ws://localhost:8765')。"
    )
    logger.info(f"2. 修改 'log/radar.log' 文件来观察广播。")
    logger.info(f"3. 在此终端按 Enter 键来模拟环境变量热更新。")
    logger.info(f"4. 按 Ctrl+C 来停止并退出。")

    try:
        # 保持主线程存活，并等待用户输入以触发热更新
        input("\n按 [Enter] 键来模拟热更新 (将监控文件夹改为 'temp_logs')...\n")

        # 模拟配置变化
        logger.info(f"\n--- 正在模拟热更新 ---")
        os.environ["WATCH_FOLDER"] = "temp_logs"
        # 确保新文件夹存在
        os.makedirs("temp_logs", exist_ok=True)

        # 等待一小会儿，让监控线程检测到变化
        time.sleep(1.5)
        logger.info(f"现在监控器应该已经切换到了 'temp_logs' 文件夹。")
        logger.info(f"修改 'temp_logs/test.log' 来观察效果。")

        # 让脚本继续运行
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        logger.info(f"\n收到退出信号...")
    finally:
        # 3. 优雅地停止监控器
        logger.info(f"正在停止监控器...")
        watcher.stop()
        logger.info(f"示例程序已退出。")
