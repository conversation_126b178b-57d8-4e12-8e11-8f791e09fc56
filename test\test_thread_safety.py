#!/usr/bin/env python3
"""
测试 RadarManager 的线程安全性
"""

import threading
import time
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), "my_code"))

from radar_code import RadarManager


def test_concurrent_access():
    """测试并发访问 RadarManager"""
    manager = RadarManager.get_instance()

    def worker(worker_id):
        """工作线程函数"""
        for i in range(10):
            # 模拟注册雷达
            radar_id = f"RADAR_{worker_id}_{i:02d}"

            # 这里我们不能创建真实的 ArcSAR 实例，所以用字符串代替进行测试
            # 在实际代码中，这里应该是 ArcSAR 实例
            fake_radar = f"FakeRadar_{radar_id}"

            # 注册雷达
            manager.register_radar(radar_id, fake_radar)  # type: ignore
            print(f"Worker {worker_id}: 注册了雷达 {radar_id}")

            # 检查雷达是否注册
            if manager.is_radar_registered(radar_id):
                print(f"Worker {worker_id}: 确认雷达 {radar_id} 已注册")

            # 获取雷达列表
            radar_list = manager.get_radar_list()
            print(f"Worker {worker_id}: 当前雷达数量: {len(radar_list)}")

            # 短暂休眠
            time.sleep(0.01)

            # 注销雷达
            manager.unregister_radar(radar_id)
            print(f"Worker {worker_id}: 注销了雷达 {radar_id}")

    # 创建多个线程
    threads = []
    for i in range(5):
        thread = threading.Thread(target=worker, args=(i,))
        threads.append(thread)

    # 启动所有线程
    print("启动所有工作线程...")
    for thread in threads:
        thread.start()

    # 等待所有线程完成
    for thread in threads:
        thread.join()

    # 检查最终状态
    final_radar_list = manager.get_radar_list()
    print(f"测试完成，最终雷达数量: {len(final_radar_list)}")

    if len(final_radar_list) == 0:
        print("✅ 线程安全测试通过：所有雷达都已正确注销")
    else:
        print(f"❌ 线程安全测试失败：还有 {len(final_radar_list)} 个雷达未注销")


if __name__ == "__main__":
    print("开始测试 RadarManager 的线程安全性...")
    test_concurrent_access()
    print("测试完成。")
