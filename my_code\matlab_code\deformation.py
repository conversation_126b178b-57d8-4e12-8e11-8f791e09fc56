import numpy as np
import tifffile
from logging import getLogger, basicConfig, INFO
import os
import json
from typing import cast, Dict, Any

# --- 配置日志，方便调试 ---
basicConfig(level=INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = getLogger(__name__)


def wrapToPi(angle):
    """
    将任意角度（弧度）规整到 [-pi, pi] 的区间内。
    此实现经过验证，性能最优。
    """
    return (angle + np.pi) % (2 * np.pi) - np.pi


def deformation(Img1Path, Img2Path, outputPath):
    """
    形变计算
    Args：
        Img1Path: 第一幅图像的路径
        Img2Path: 第二幅图像的路径
        outputPath: 输出路径
    Returns：
        无返回值，结果保存在outputPath中
    """
    if not os.path.exists(Img1Path):
        logger.error(f"文件不存在: {Img1Path}")
        return
    if not os.path.exists(Img2Path):
        logger.error(f"文件不存在: {Img2Path}")
        return

    try:
        # --- 读取第一幅图像及其元数据 ---
        logger.info(f"正在读取文件: {Img1Path}")
        with tifffile.TiffFile(Img1Path) as tif:
            image_data1 = tif.asarray()

            # 首先尝试使用高级API直接获取元数据字典
            metadata_dict = tif.json_metadata

            # 如果高级API失败，则回退到手动解析（并忽略Pylance警告）
            if metadata_dict is None:
                logger.warning("未找到JSON元数据，尝试手动解析ImageDescription...")
                # 在这一行，我们告诉Pylance忽略这个它无法理解的动态属性访问
                description_string = tif.pages[0].tags["ImageDescription"].value  # type: ignore
                metadata_dict = cast(Dict[str, Any], json.loads(description_string))

                logger.info("元数据解析成功，正在提取参数...")
                theta0, dTheta, r0, dr, coordinates, radar_zero_theta = (
                    metadata_dict.get("theta0", 0),
                    metadata_dict.get("dTheta", 0),
                    metadata_dict.get("r0", 0),
                    metadata_dict.get("dr", 0),
                    metadata_dict.get("coordinates", []),
                    metadata_dict.get("radar_zero_theta", 0),
                )
                logger.info("参数提取成功")

        # =======================================================
        # 在这里继续您的InSAR处理流程（复数重构、干涉相位计算等）
        # logger.info("开始进行InSAR形变计算...")
        # ... 您的后续代码 ...
        # =======================================================

    except Exception as e:
        logger.error(
            f"处理文件时发生意外错误: {e}", exc_info=True
        )  # exc_info=True 会记录完整的错误堆栈
        return
