<!-- frontend.html -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>实时文件更新监控</title>
    <style>
        body { font-family: sans-serif; background-color: #f0f2f5; color: #333; }
        #container { max-width: 800px; margin: 40px auto; padding: 20px; background-color: #fff; box-shadow: 0 0 10px rgba(0,0,0,0.1); border-radius: 8px; }
        #status { font-weight: bold; padding: 10px; border-radius: 4px; text-align: center; }
        .connected { color: #28a745; background-color: #e9f7eb; }
        .disconnected { color: #dc3545; background-color: #fce8e6; }
        pre { white-space: pre-wrap; word-wrap: break-word; background-color: #272822; color: #f8f8f2; padding: 15px; border-radius: 4px; max-height: 60vh; overflow-y: auto;}
    </style>
</head>
<body>
    <div id="container">
        <h1>实时文件更新监控</h1>
        <div id="status">正在连接...</div>
        <h3>接收到的最新文件内容:</h3>
        <pre id="content">等待服务器推送文件...</pre>
    </div>

    <script>
        const statusDiv = document.getElementById('status');
        const contentDiv = document.getElementById('content');

        // 注意这里的 URL！我们连接到 Nginx 代理的地址，而不是后端应用的 8765 端口。
        // 使用 wss:// (生产环境) 或 ws:// (本地测试)
        const socket = new WebSocket(`ws://localhost:8000/ws/`);

        // 设置接收二进制数据的类型
        socket.binaryType = 'arraybuffer';

        socket.onopen = function(event) {
            console.log("WebSocket 连接已建立");
            statusDiv.textContent = '已连接到服务器';
            statusDiv.className = 'connected';
        };

        socket.onmessage = function(event) {
            console.log("从服务器收到消息");
            // event.data 是一个 ArrayBuffer
            if (event.data instanceof ArrayBuffer) {
                // 尝试将 ArrayBuffer 解码为 UTF-8 文本
                try {
                    const decoder = new TextDecoder('utf-8');
                    const text = decoder.decode(event.data);
                    contentDiv.textContent = text;
                    console.log("已解码为文本内容。");
                } catch (e) {
                    // 如果解码失败 (例如，它是一个图片)，就显示提示信息
                    contentDiv.textContent = `[接收到二进制数据 (${event.data.byteLength} 字节)，无法显示为文本]`;
                    console.error("无法将二进制数据解码为文本:", e);
                }
            } else {
                // 以防万一收到的是文本帧
                contentDiv.textContent = event.data;
            }
        };

        socket.onclose = function(event) {
            console.log("WebSocket 连接已关闭", event);
            statusDiv.textContent = '连接已断开';
            statusDiv.className = 'disconnected';
        };

        socket.onerror = function(error) {
            console.error("WebSocket 发生错误:", error);
            statusDiv.textContent = '连接错误';
            statusDiv.className = 'disconnected';
        };
    </script>
</body>
</html>