yaozhj  backend   backend   master  ♥ 01:01:00  uv run c:\Users\<USER>\Documents\Arcweb\backend\my_code\radar_code.py
2025-06-14 01:03:07,368 - INFO - 服务器线程已启动。
2025-06-14 01:03:07,369 - INFO - 雷达服务器已启动，监听端口127.0.0.1:1030
2025-06-14 01:03:08,371 - INFO - 雷达模拟器线程已启动。
2025-06-14 01:03:08,371 - INFO - [模拟器] 已连接到服务器 127.0.0.1:1030
2025-06-14 01:03:08,371 - INFO - [模拟器] 发送初始化注册包...
2025-06-14 01:03:08,372 - INFO - 来自('127.0.0.1', 61564)的新连接
2025-06-14 01:03:08,372 - INFO - 正从('127.0.0.1', 61564)处接受头部数据，目标长度为28
2025-06-14 01:03:08,372 - INFO - 成功在('127.0.0.1', 61564)接收到头部数据，长度为28  
2025-06-14 01:03:08,373 - INFO - 头部数据为
  00000000:  3C 3C 00 10 05 AD 40 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-06-14 01:03:08,373 - INFO - 从('127.0.0.1', 61564)解析得到的数据长度为0
2025-06-14 01:03:08,373 - INFO - 开始从('127.0.0.1', 61564)处接受数据，目标长度为0
2025-06-14 01:03:08,373 - INFO - 成功在('127.0.0.1', 61564)接收到主要数据，长度为0
2025-06-14 01:03:08,373 - INFO - 主要数据为<empty>
2025-06-14 01:03:08,373 - INFO - 来自('127.0.0.1', 61564)的雷达的设备ID为: AD051000
2025-06-14 01:03:08,396 - INFO - 设备ID为AD051000的雷达是新雷达，开始初始化对应的数据库
2025-06-14 01:03:08,406 - INFO - 为设备ID为AD051000的雷达创建了必要的文件目录结构
2025-06-14 01:03:08,800 - INFO - 成功为设备ID为AD051000的雷达创建了数据库
2025-06-14 01:03:08,800 - INFO - 设备ID为AD051000的雷达发送了数据，命令码=0x40, 扩展码=0x00, 主要数据长度=0
2025-06-14 01:03:08,800 - INFO - 分配处理函数为update_radar_imformation
2025-06-14 01:03:08,801 - INFO - 开始解析雷达AD051000的基本信息
2025-06-14 01:03:08,801 - INFO - 开始更新雷达AD051000数据库中的基本信息
2025-06-14 01:03:08,802 - ERROR - 数据解析错误，从负载数据的第2位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,802 - ERROR - 数据解析错误，从负载数据的第6位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,802 - ERROR - 数据解析错误，从负载数据的第10位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,802 - ERROR - 数据解析错误，从负载数据的第14位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,803 - ERROR - 数据解析错误，从负载数据的第18位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,803 - ERROR - 数据解析错误，从负载数据的第22位开始发生位置: unpack requires a buffer of 2 bytes
2025-06-14 01:03:08,803 - ERROR - 数据解析错误，从负载数据的第30位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,803 - ERROR - 数据解析错误，从负载数据的第34位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,803 - ERROR - 数据解析错误，从负载数据的第38位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,803 - ERROR - 数据解析错误，从负载数据的第42位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,803 - ERROR - 数据解析错误，从负载数据的第46位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,803 - ERROR - 数据解析错误，从负载数据的第50位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,803 - ERROR - 数据解析错误，从负载数据的第54位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,803 - ERROR - 数据解析错误，从负载数据的第58位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,803 - ERROR - 数据解析错误，从负载数据的第62位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,803 - ERROR - 数据解析错误，从负载数据的第66位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,804 - ERROR - 数据解析错误，从负载数据的第70位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,804 - ERROR - 数据解析错误，从负载数据的第74位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,804 - ERROR - 数据解析错误，从负载数据的第78位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,804 - ERROR - 数据解析错误，从负载数据的第82位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,804 - ERROR - 数据解析错误，从负载数据的第86位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,804 - ERROR - 数据解析错误，从负载数据的第90位开始发生位置: unpack requires a buffer of 8 bytes
2025-06-14 01:03:08,804 - ERROR - 数据解析错误，从负载数据的第98位开始发生位置: unpack requires a buffer of 8 bytes
2025-06-14 01:03:08,804 - ERROR - 数据解析错误，从负载数据的第106位开始发生位置: unpack requires a buffer of 8 bytes
2025-06-14 01:03:08,804 - ERROR - 数据解析错误，从负载数据的第114位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,804 - ERROR - 数据解析错误，从负载数据的第118位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,804 - ERROR - 数据解析错误，从负载数据的第122位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,805 - ERROR - 数据解析错误，从负载数据的第126位开始发生位置: unpack requires a buffer of 1 bytes
2025-06-14 01:03:08,805 - ERROR - 数据解析错误，从负载数据的第127位开始发生位置: unpack requires a buffer of 1 bytes
2025-06-14 01:03:08,805 - ERROR - 数据解析错误，从负载数据的第128位开始发生位置: unpack requires a buffer of 1 bytes
2025-06-14 01:03:08,805 - ERROR - 数据解析错误，从负载数据的第129位开始发生位置: unpack requires a buffer of 1 bytes
2025-06-14 01:03:08,805 - ERROR - 数据解析错误，从负载数据的第130位开始发生位置: unpack requires a buffer of 1 bytes
2025-06-14 01:03:08,805 - ERROR - 数据解析错误，从负载数据的第131位开始发生位置: unpack requires a buffer of 1 bytes
2025-06-14 01:03:08,805 - ERROR - 数据解析错误，从负载数据的第132位开始发生位置: unpack requires a buffer of 1 bytes
2025-06-14 01:03:08,805 - ERROR - 数据解析错误，从负载数据的第133位开始发生位置: unpack requires a buffer of 1 bytes
2025-06-14 01:03:08,805 - ERROR - 数据解析错误，从负载数据的第134位开始发生位置: unpack requires a buffer of 1 bytes
2025-06-14 01:03:08,805 - ERROR - 数据解析错误，从负载数据的第135位开始发生位置: unpack requires a buffer of 1 bytes
2025-06-14 01:03:08,805 - ERROR - 数据解析错误，从负载数据的第136位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,805 - ERROR - 数据解析错误，从负载数据的第140位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,805 - ERROR - 数据解析错误，从负载数据的第144位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,806 - ERROR - 数据解析错误，从负载数据的第148位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,806 - ERROR - 数据解析错误，从负载数据的第152位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,806 - ERROR - 数据解析错误，从负载数据的第156位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,806 - ERROR - 数据解析错误，从负载数据的第168位开始发生位置: unpack requires a buffer of 1 bytes
2025-06-14 01:03:08,806 - ERROR - 数据解析错误，从负载数据的第169位开始发生位置: unpack requires a buffer of 1 bytes
2025-06-14 01:03:08,806 - ERROR - 数据解析错误，从负载数据的第170位开始发生位置: unpack requires a buffer of 1 bytes
2025-06-14 01:03:08,806 - ERROR - 数据解析错误，从负载数据的第171位开始发生位置: unpack requires a buffer of 1 bytes
2025-06-14 01:03:08,806 - ERROR - 数据解析错误，从负载数据的第172位开始发生位置: unpack requires a buffer of 1 bytes
2025-06-14 01:03:08,806 - ERROR - 数据解析错误，从负载数据的第173位开始发生位置: unpack requires a buffer of 1 bytes
2025-06-14 01:03:08,807 - ERROR - 数据解析错误，从负载数据的第174位开始发生位置: unpack requires a buffer of 4 bytes
2025-06-14 01:03:08,807 - INFO - 成功更新雷达AD051000数据库中的基本信息
2025-06-14 01:03:08,807 - INFO - [模拟器] 收到对初始化包的响应，忽略。
2025-06-14 01:03:08,807 - INFO - 成功向ID为AD051000的雷达发送响应数据，长度为0，内容为空
2025-06-14 01:03:09,371 - INFO - 连接成功，开始执行 test_radar_control。
测试雷达 AD051000 的控制功能
2025-06-14 01:03:11,372 - INFO - 开始查询雷达AD051000的状态
2025-06-14 01:03:11,372 - INFO - 已将counter=1的状态查询指令放入队列
2025-06-14 01:03:11,841 - INFO - [模拟器] 收到指令: cmd=0x02, ext=0x00, counter=1
2025-06-14 01:03:11,841 - INFO - 成功向雷达AD051000发送指令
2025-06-14 01:03:11,842 - INFO - [模拟器] 已为 counter 1 发送响应
2025-06-14 01:03:11,842 - INFO - 正从('127.0.0.1', 61564)处接受头部数据，目标长度为28
2025-06-14 01:03:11,842 - INFO - 成功在('127.0.0.1', 61564)接收到头部数据，长度为28
2025-06-14 01:03:11,842 - INFO - 头部数据为
  00000000:  5A 5A 00 10 05 AD 02 00 01 01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-06-14 01:03:11,842 - INFO - 从('127.0.0.1', 61564)解析得到的数据长度为0
2025-06-14 01:03:11,842 - INFO - 开始从('127.0.0.1', 61564)处接受数据，目标长度为0
2025-06-14 01:03:11,842 - INFO - 成功在('127.0.0.1', 61564)接收到主要数据，长度为0
2025-06-14 01:03:11,842 - INFO - 主要数据为<empty>
2025-06-14 01:03:11,842 - INFO - 收到雷达AD051000的响应，counter=1
2025-06-14 01:03:11,842 - INFO - 雷达AD051000状态查询成功，状态为设置正确
状态查询结果: success
2025-06-14 01:03:11,842 - INFO - 开始向雷达AD051000发送工作控制指令:开始工作
2025-06-14 01:03:11,843 - INFO - 已将counter=2的工作控制指令放入队列:开始工作
2025-06-14 01:03:12,845 - INFO - [模拟器] 收到指令: cmd=0x03, ext=0x00, counter=2
2025-06-14 01:03:12,846 - INFO - 成功向雷达AD051000发送指令
2025-06-14 01:03:12,846 - INFO - 正从('127.0.0.1', 61564)处接受头部数据，目标长度为28
2025-06-14 01:03:12,846 - INFO - [模拟器] 已为 counter 2 发送响应
2025-06-14 01:03:12,846 - INFO - 成功在('127.0.0.1', 61564)接收到头部数据，长度为28
2025-06-14 01:03:12,847 - INFO - 头部数据为
  00000000:  5A 5A 00 10 05 AD 02 00 01 02 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-06-14 01:03:12,847 - INFO - 从('127.0.0.1', 61564)解析得到的数据长度为0
2025-06-14 01:03:12,847 - INFO - 开始从('127.0.0.1', 61564)处接受数据，目标长度为0
2025-06-14 01:03:12,848 - INFO - 成功在('127.0.0.1', 61564)接收到主要数据，长度为0
2025-06-14 01:03:12,848 - INFO - 主要数据为<empty>
2025-06-14 01:03:12,848 - INFO - 收到雷达AD051000的响应，counter=2
2025-06-14 01:03:12,848 - INFO - 雷达AD051000工作控制成功
2025-06-14 01:03:12,855 - INFO - 成功创建雷达AD051000的工作数据文件夹
2025-06-14 01:03:12,856 - INFO - 雷达AD051000开始工作
开始工作结果: success
2025-06-14 01:03:12,857 - INFO - 测试结束。