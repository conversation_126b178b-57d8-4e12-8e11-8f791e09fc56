"""
高级验证和存在性检查框架
提供链式验证、自动错误处理和简化的API
支持多种数据库操作、自定义数据库源和增强异常处理
"""

from flask import jsonify, request, Response
from typing import (
    Any,
    Dict,
    List,
    Optional,
    Callable,
    TypeVar,
    Generic,
    Tuple,
)

from typing import Self
from pymongo.mongo_client import MongoClient
from pymongo.collection import Collection
from pymongo.errors import (
    ConnectionFailure,
    OperationFailure,
    DuplicateKeyError,
    WriteError,
    WriteConcernError,
    NetworkTimeout,
    ServerSelectionTimeoutError,
    AutoReconnect,
    ConfigurationError,
    InvalidOperation,
    BulkWriteError,
)
from bson.errors import InvalidDocument, InvalidId
from bson import ObjectId
from functools import wraps
import logging

# --- 1. 设置与核心类型定义 ---

logger = logging.getLogger(__name__)

T = TypeVar("T")
ApiResponse = Tuple[Response, int]
F = Callable[..., ApiResponse]


# --- 2. 核心异常类 ---


class ValidationError(Exception):
    """
    自定义验证错误异常。
    此类异常代表可预期的、业务逻辑层面的错误，应被捕获并转换为对用户友好的API响应。
    """

    def __init__(
        self, message: str, code: str = "VALIDATION_ERROR", status_code: int = 400
    ):
        """
        初始化验证错误

        Args:
            message (str): 错误信息
            code (str, optional): 错误代码，默认为"VALIDATION_ERROR"
            status_code (int, optional): HTTP状态码，默认为400
        """
        self.message = message
        self.code = code
        self.status_code = status_code
        super().__init__(message)

    def to_response(self) -> Tuple[Response, int]:
        """
        将验证错误转换为Flask响应

        Returns:
            Tuple[Response, int]: 包含错误信息的JSON响应和HTTP状态码的元组
        """
        return jsonify({"error": self.message, "code": self.code}), self.status_code


# --- 3. 验证器核心类 ---


class ValidationResult(Generic[T]):
    """验证结果包装器"""

    def __init__(
        self, value: Optional[T] = None, error: Optional[ValidationError] = None
    ):
        """
        初始化验证器实例

        Args:
            value (Optional[T]): 待验证的值，可为None
            error (Optional[ValidationError]): 验证错误对象，None表示验证通过

        Attributes:
            value: 存储传入的待验证值
            error: 存储验证错误信息
            is_valid: 布尔值，表示验证是否通过(error为None时为True)
        """
        self.value = value
        self.error = error
        self.is_valid = error is None

    def unwrap(self) -> T:
        """解包结果对象，返回内部值。如果存在错误则抛出异常。

        Returns:
            内部存储的值。

        Raises:
            Exception: 如果结果对象包含错误则抛出。
        """
        if self.error:
            raise self.error
        if self.value is None:
            raise ValidationError("解包的值无效", "UNWRAP_ERROR")
        return self.value  # type: ignore

    def unwrap_or(self, default: T) -> T:
        """如果当前值为有效则返回该值，否则返回指定的默认值。

        Args:
            default (T): 当值无效时返回的默认值

        Returns:
            T: 有效值或默认值
        """
        return self.value if self.is_valid and self.value is not None else default


class ChainValidator:
    """链式验证器 - 支持流畅的验证链"""

    def __init__(self, value: Any = None, context: str = ""):
        """
        初始化验证器实例

        Args:
            value (Any): 待验证的值，默认为None
            context (str): 验证上下文信息，默认为空字符串
        Attributes:
            errors: 存储验证错误的列表
        """
        self.value = value
        self.context = context
        self.errors: List[ValidationError] = []
        self._stopped = False  # 如果已触发 required 错误，则停止后续检查

    def _check_and_continue(self) -> bool:
        """内部方法，检查是否应继续验证链。
        当且仅当值不为None，且通过 required 检查时，才继续执行后续验证。
        """
        return not self._stopped and self.value is not None

    def required(self, message: Optional[str] = None) -> Self:
        """
        检查字段值是否存在

        Args:
            message: 自定义错误消息，默认为"{字段名}不能为空"

        Returns:
            ChainValidator: 返回当前验证器实例以支持链式调用

        Raises:
            无显式抛出异常，但会在self.errors中添加ValidationError
        """
        if self.value is None:
            error_msg = message or f"{self.context}不能为空"
            self.errors.append(ValidationError(error_msg, "REQUIRED_FIELD_MISSING"))
            self._stopped = True
        return self

    def not_empty(self, message: Optional[str] = None) -> Self:
        """验证值是否为空，
        检查字符串、列表或字典类型的值是否为空（空字符串、空列表或空字典）

        Args:
            message (Optional[str]): 自定义错误消息，默认为"{context}不能为空"

        Returns:
            ChainValidator: 返回当前验证器实例以支持链式调用

        Raises:
            无显式抛出异常，但会将错误添加到self.errors列表中
        """
        if self._check_and_continue() and (
            (isinstance(self.value, str) and not self.value.strip())
            or (isinstance(self.value, (list, dict)) and len(self.value) == 0)  # type: ignore
        ):
            error_msg = message or f"{self.context}不能为空"
            self.errors.append(ValidationError(error_msg, "EMPTY_VALUE"))
        return self

    def min_length(self, length: int, message: Optional[str] = None) -> Self:
        """
        验证值的长度是否不小于指定最小长度。

        Args:
            length (int): 最小长度要求
            message (Optional[str]): 自定义错误信息，默认为None

        Returns:
            ChainValidator: 返回当前验证器实例以支持链式调用

        Raises:
            当值长度小于指定长度时，会添加MIN_LENGTH_ERROR验证错误
        """
        if (
            self._check_and_continue()
            and hasattr(self.value, "__len__")
            and len(self.value) < length
        ):
            error_msg = message or f"{self.context}长度不能少于{length}"
            self.errors.append(ValidationError(error_msg, "MIN_LENGTH_ERROR"))
        return self

    def max_length(self, length: int, message: Optional[str] = None) -> Self:
        """验证字段值的最大长度限制

        Args:
            length: 允许的最大长度
            message: 自定义错误信息，默认为"字段长度不能超过{length}"

        Returns:
            ChainValidator: 返回当前验证器实例以支持链式调用

        Raises:
            当字段值长度超过限制时，会添加MAX_LENGTH_ERROR到errors列表
        """
        if (
            self._check_and_continue()
            and hasattr(self.value, "__len__")
            and len(self.value) > length
        ):
            error_msg = message or f"{self.context}长度不能超过{length}"
            self.errors.append(ValidationError(error_msg, "MAX_LENGTH_ERROR"))
        return self

    def is_valid_object_id(self, message: Optional[str] = None) -> Self:
        """验证值是否为有效的MongoDB ObjectId

        Args:
            message: 自定义错误信息

        Returns:
            ChainValidator: 返回当前验证器实例以支持链式调用
        """
        if self._check_and_continue():
            try:
                ObjectId(self.value)
            except (InvalidId, TypeError):
                error_msg = message or f"{self.context}不是有效的ObjectId格式"
                self.errors.append(ValidationError(error_msg, "INVALID_OBJECT_ID"))
        return self

    def custom(
        self,
        validator_func: Callable[[Any], bool],
        message: str,
        code: str = "CUSTOM_VALIDATION_ERROR",
    ) -> Self:
        """
        自定义验证方法

        Args:
            validator_func: 自定义验证函数，接收任意类型参数并返回布尔值
            message: 验证失败时的错误信息
            code: 验证失败时的错误代码，默认为"CUSTOM_VALIDATION_ERROR"

        Returns:
            ChainValidator: 返回当前验证器实例以支持链式调用

        Raises:
            如果当前值不为None且验证函数返回False，则会将错误信息添加到errors列表中
        """
        if self._check_and_continue() and not validator_func(self.value):
            self.errors.append(ValidationError(message, code))
        return self

    def result(self) -> ValidationResult[Any]:
        """
        获取验证结果

        Returns:
            ValidationResult: 如果存在错误则返回第一个错误信息，否则返回验证通过的值
        """
        if self.errors:
            return ValidationResult(error=self.errors[0])
        return ValidationResult(value=self.value)

    def unwrap(self) -> Any:
        """如果验证失败，立即抛出异常，否则返回值。"""
        return self.result().unwrap()

    def unwrap_or(self, default: T) -> T:
        """
        如果验证通过，则返回值；如果验证失败或值不存在，则返回指定的默认值。

        Args:
            default: 默认值

        Returns:
            Any: 验证通过的值或默认值
        """
        return self.result().unwrap_or(default)


class DatabaseHelper:
    """数据库查询助手 - 自动处理存在性检查和CRUD操作"""

    def __init__(
        self,
        collection: Collection[Dict[str, Any]],
    ):
        # 初始化验证器实例
        self.collection = collection

    def find_one_unwrap(
        self,
        query: Dict[str, Any],
        error_message: str = "记录不存在",
        error_code: str = "RECORD_NOT_FOUND",
        projection: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        根据查询条件查找单个文档，若不存在则抛出验证错误

        Args:
            query (Dict[str, Any]): 查询条件字典
            error_message (str, optional): 记录不存在时的错误信息
            error_code (str, optional): 记录不存在时的错误代码
            projection (Optional[Dict[str, Any]]): 字段投影

        Returns:
            Dict[str, Any]: 查询到的文档

        Raises:
            ValidationError: 当查询结果为空时抛出404错误
        """
        doc = self.collection.find_one(query, projection)
        if doc is None:
            raise ValidationError(error_message, error_code, 404)
        return doc

    def find_many_or_empty(
        self,
        query: Dict[str, Any],
        limit: Optional[int] = None,
        skip: Optional[int] = None,
        sort: Optional[List[Tuple[str, int]]] = None,
        projection: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """
        根据查询条件查找多个文档，若不存在则返回空列表

        Args:
            query: 查询条件字典
            limit: 限制返回的文档数量
            skip: 跳过的文档数量
            sort: 排序条件，例如 [("name", 1), ("age", -1)]
            projection: 字段投影

        Returns:
            List[Dict[str, Any]]: 查询到的文档列表
        """
        cursor = self.collection.find(query, projection)

        if skip is not None:
            cursor = cursor.skip(skip)
        if limit:
            cursor = cursor.limit(limit)
        if sort:
            cursor = cursor.sort(sort)

        return list(cursor)

    def find_many_unwrap(
        self,
        query: Dict[str, Any],
        error_message: str = "没有找到任何记录",
        error_code: str = "NO_RECORDS_FOUND",
        limit: Optional[int] = None,
        skip: Optional[int] = None,
        sort: Optional[List[Tuple[str, int]]] = None,
        projection: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """
        根据查询条件查找多个文档，若不存在则抛出验证错误

        Args:
            query: 查询条件字典
            error_message: 没有找到记录时的错误信息
            error_code: 没有找到记录时的错误代码
            limit: 限制返回的文档数量
            skip: 跳过的文档数量
            sort: 排序条件
            projection: 字段投影

        Returns:
            List[Dict[str, Any]]: 查询到的文档列表

        Raises:
            ValidationError: 当查询结果为空时抛出404错误
        """
        docs = self.find_many_or_empty(query, limit, skip, sort, projection)
        if not docs:
            raise ValidationError(error_message, error_code, 404)
        return docs

    def count_documents(self, query: Dict[str, Any]) -> int:
        """
        统计符合条件的文档数量

        Args:
            query: 查询条件字典

        Returns:
            int: 文档数量
        """
        return self.collection.count_documents(query)

    def exists(self, query: Dict[str, Any]) -> bool:
        """
        检查是否存在符合条件的文档

        Args:
            query: 查询条件字典

        Returns:
            bool: 是否存在
        """
        return self.count_documents(query) > 0

    def insert_one_and_return(
        self,
        document: Dict[str, Any],
        return_document: bool = True,
    ) -> Dict[str, Any]:
        """
        插入单个文档并返回插入的文档

        Args:
            document: 要插入的文档
            return_document: 是否返回插入的文档

        Returns:
            Dict[str, Any]: 插入的文档（包含_id）

        Raises:
            ValidationError: 当插入失败时抛出
        """
        result = self.collection.insert_one(document)
        if not result.acknowledged:
            raise ValidationError("文档插入失败", "INSERT_FAILED", 500)

        if return_document:
            document["_id"] = result.inserted_id
            return document
        else:
            return {"_id": result.inserted_id, "acknowledged": True}

    def insert_many_and_return(
        self,
        documents: List[Dict[str, Any]],
        return_documents: bool = True,
    ) -> List[Dict[str, Any]]:
        """
        插入多个文档并返回插入的文档

        Args:
            documents: 要插入的文档列表
            return_documents: 是否返回插入的文档

        Returns:
            List[Dict[str, Any]]: 插入的文档列表（包含_id）

        Raises:
            ValidationError: 当插入失败时抛出
        """
        if not documents:
            return []

        result = self.collection.insert_many(documents)
        if not result.acknowledged:
            raise ValidationError("文档批量插入失败", "BULK_INSERT_FAILED", 500)

        if return_documents:
            for doc, inserted_id in zip(documents, result.inserted_ids):
                doc["_id"] = inserted_id
            return documents
        else:
            return [{"_id": _id, "acknowledged": True} for _id in result.inserted_ids]

    def update_one_and_return(
        self,
        query: Dict[str, Any],
        update: Dict[str, Any],
        upsert: bool = False,
        return_updated: bool = True,
    ) -> Optional[Dict[str, Any]]:
        """
        更新单个文档并返回更新后的文档

        Args:
            query: 查询条件
            update: 更新操作
            upsert: 是否在不存在时插入
            return_updated: 是否返回更新后的文档

        Returns:
            Optional[Dict[str, Any]]: 更新后的文档，如果没有找到则返回None

        Raises:
            ValidationError: 当更新失败时抛出
        """
        if return_updated:
            # 使用 find_one_and_update 返回更新后的文档
            from pymongo import ReturnDocument

            result = self.collection.find_one_and_update(
                query, update, upsert=upsert, return_document=ReturnDocument.AFTER
            )
            return result
        else:
            result = self.collection.update_one(query, update, upsert=upsert)
            if result.matched_count == 0 and not upsert:
                return None
            return {
                "matched_count": result.matched_count,
                "modified_count": result.modified_count,
            }

    def update_many_and_count(
        self,
        query: Dict[str, Any],
        update: Dict[str, Any],
    ) -> Dict[str, int]:
        """
        更新多个文档并返回统计信息

        Args:
            query: 查询条件
            update: 更新操作

        Returns:
            Dict[str, int]: 包含matched_count和modified_count的字典
        """
        result = self.collection.update_many(query, update)
        return {
            "matched_count": result.matched_count,
            "modified_count": result.modified_count,
        }

    def delete_one_and_return(
        self,
        query: Dict[str, Any],
        # error_if_not_found: bool = True,
    ) -> Optional[Dict[str, Any]]:
        """
        删除单个文档并返回被删除的文档

        Args:
            query: 查询条件
            error_if_not_found: 如果没有找到文档是否抛出错误

        Returns:
            Optional[Dict[str, Any]]: 被删除的文档，如果没有找到则返回None

        Raises:
            ValidationError: 当没有找到文档且error_if_not_found为True时抛出
        """
        doc = self.collection.find_one_and_delete(query)
        # if doc is None and error_if_not_found:
        # raise ValidationError("要删除的记录不存在", "DELETE_TARGET_NOT_FOUND", 404)
        return doc

    def delete_many_and_count(
        self,
        query: Dict[str, Any],
        error_if_none_deleted: bool = False,
    ) -> int:
        """
        删除多个文档并返回删除的数量

        Args:
            query: 查询条件
            error_if_none_deleted: 如果没有删除任何文档是否抛出错误

        Returns:
            int: 删除的文档数量

        Raises:
            ValidationError: 当没有删除任何文档且error_if_none_deleted为True时抛出
        """
        result = self.collection.delete_many(query)
        if result.deleted_count == 0 and error_if_none_deleted:
            raise ValidationError("没有找到要删除的记录", "NO_RECORDS_TO_DELETE", 404)
        return result.deleted_count

    def find_field_or_fail(
        self,
        query: Dict[str, Any],
        field: str,
        error_message: Optional[str] = None,
        error_code: str = "FIELD_NOT_FOUND",
    ) -> Any:
        """
        在查询结果中查找指定字段，若字段不存在则抛出验证错误

        Args:
            query: 查询条件字典
            field: 要查找的字段名
            error_message: 自定义错误信息，默认为"字段 {field} 不存在"
            error_code: 错误代码，默认为"FIELD_NOT_FOUND"

        Returns:
            查询到的字段值

        Raises:
            ValidationError: 当字段不存在时抛出，包含错误信息和404状态码
        """
        doc = self.find_one_unwrap(query)
        value = doc.get(field)
        if value is None:
            error_msg = error_message or f"字段 {field} 不存在"
            raise ValidationError(error_msg, error_code, 404)
        return value

    def find_non_empty_field(
        self,
        query: Dict[str, Any],
        field: str,
        error_message: Optional[str] = None,
        error_code: str = "FIELD_EMPTY",
    ) -> Any:
        """
        在查询字典中查找指定字段并验证其非空。

        Args:
            query: 要查询的字典
            field: 要查找的字段名
            error_message: 自定义错误信息，默认为None
            error_code: 错误代码，默认为"FIELD_EMPTY"

        Returns:
            找到的字段值

        Raises:
            ValidationError: 如果字段不存在或值为空(list/dict)
        """
        value = self.find_field_or_fail(query, field, error_message, error_code)
        if isinstance(value, (list, dict)) and len(value) == 0:  # type: ignore
            error_msg = error_message or f"字段 {field} 为空"
            raise ValidationError(error_msg, error_code, 404)
        return value  # type: ignore


class RequestValidator:
    """请求验证器 - 简化请求数据验证"""

    @staticmethod
    def get_json_unwrap() -> Dict[str, Any]:
        """
        静态方法 - 获取并验证请求中的JSON数据

        从请求中获取JSON格式数据，如果数据无效或不存在则抛出ValidationError异常

        它允许空的JSON对象

        Returns:
            Dict[str, Any]: 解析成功的JSON字典数据

        Raises:
            ValidationError: 当请求体不是有效JSON格式时抛出，附带错误信息"请求体必须是有效的JSON格式"和错误代码"INVALID_REQUEST_BODY"
        """
        data = request.get_json(silent=True)
        if data is None:
            raise ValidationError("请求体必须是有效的JSON格式", "INVALID_REQUEST_BODY")
        return data

    @staticmethod
    def extract_and_validate(data: Dict[str, Any], *fields: str) -> Dict[str, Any]:
        """
        从请求数据中提取指定字段并进行基本验证

        Args:
            data (Dict[str, Any]): 待验证的JSON数据

        Returns:
            Dict[str, Any]: 验证后的数据字典

        Raises:
            ValidationError: 当字段不存在时抛出，附带错误信息"{field}不能为空"和错误代码"REQUIRED_FIELD_MISSING"
        """
        validated_data: Dict[str, Any] = {}
        for field in fields:
            value = data.get(field)
            # 仅做存在性检查，更复杂的验证留给业务逻辑层
            validated_data[field] = validate(value, field).required().unwrap()
        return validated_data


# 便捷函数
def validate(value: Any, context: str = "") -> ChainValidator:
    """创建链式验证器的便捷函数"""
    return ChainValidator(value, context)


def get_collection(
    client: MongoClient[Dict[str, Any]],
    db_name: str,
    collection_name: str,
) -> DatabaseHelper:
    """
    创建数据库操作助手的便捷函数。
    使用 cast 来为类型检查器提供明确的类型信息。
    """
    database = client[db_name]
    collection = database[collection_name]
    return DatabaseHelper(collection=collection)


# --- 装饰器 ---


def handle_api_exceptions(info: str = "") -> Callable[[F], F]:
    """最外层异常捕获器，处理未知服务器错误。"""

    def decorator(f: F) -> F:
        @wraps(f)
        def decorated_function(*args: Any, **kwargs: Any) -> ApiResponse:
            try:
                return f(*args, **kwargs)
            except ValidationError as e:
                # 捕获可预期的验证错误并返回格式化响应
                logger.warning(f"Validation failed: {e.message} (Code: {e.code})")
                return e.to_response()
            except Exception as e:
                # 捕获所有其他未知异常
                error_info = f"{info}失败" if info else "服务器内部错误"
                logger.error(f"{error_info}: {e}", exc_info=True)
                return jsonify({"error": error_info, "details": str(e)}), 500

        return decorated_function  # type: ignore[return-value]

    return decorator


def handle_database_exceptions(f: F) -> F:
    """数据库特定异常捕获器，支持PyMongo和Flask-PyMongo的异常。"""

    @wraps(f)
    def decorated_function(*args: Any, **kwargs: Any) -> ApiResponse:
        try:
            return f(*args, **kwargs)
        except (ServerSelectionTimeoutError, NetworkTimeout, AutoReconnect) as e:
            logger.error(f"数据库网络错误: {e}")
            return jsonify({"error": "数据库连接超时", "code": "DATABASE_TIMEOUT"}), 503
        except ConnectionFailure as e:
            logger.error(f"数据库连接失败: {e}")
            return (
                jsonify(
                    {"error": "数据库服务不可用", "code": "DATABASE_CONNECTION_ERROR"}
                ),
                503,
            )
        except DuplicateKeyError as e:
            logger.warning(f"数据重复错误: {e}")
            return (
                jsonify(
                    {"error": "数据已存在，不能重复创建", "code": "DUPLICATE_KEY_ERROR"}
                ),
                409,
            )
        except WriteError as e:
            logger.error(f"数据库写入错误: {e}")
            return jsonify({"error": "数据写入失败", "code": "WRITE_ERROR"}), 400
        except WriteConcernError as e:
            logger.error(f"数据库写入关注错误: {e}")
            return (
                jsonify({"error": "数据写入确认失败", "code": "WRITE_CONCERN_ERROR"}),
                500,
            )
        except BulkWriteError as e:
            logger.error(f"批量写入错误: {e}")
            return jsonify({"error": "批量操作失败", "code": "BULK_WRITE_ERROR"}), 400
        except OperationFailure as e:
            logger.error(f"数据库操作失败: {e}")
            return (
                jsonify({"error": "数据库操作失败", "code": "OPERATION_FAILURE"}),
                500,
            )
        except InvalidDocument as e:
            logger.error(f"无效文档格式: {e}")
            return (
                jsonify({"error": "提交的数据格式错误", "code": "INVALID_DOCUMENT"}),
                400,
            )
        except InvalidId as e:
            logger.error(f"无效ID格式: {e}")
            return jsonify({"error": "ID格式错误", "code": "INVALID_ID"}), 400
        except ConfigurationError as e:
            logger.error(f"数据库配置错误: {e}")
            return (
                jsonify({"error": "数据库配置错误", "code": "DATABASE_CONFIG_ERROR"}),
                500,
            )
        except InvalidOperation as e:
            logger.error(f"无效操作: {e}")
            return (
                jsonify({"error": "无效的数据库操作", "code": "INVALID_OPERATION"}),
                400,
            )

    return decorated_function


def validate_request(*required_fields: str) -> Callable[[F], F]:
    """
    请求验证装饰器，自动验证JSON请求体和指定的必需字段。
    如果验证成功，会将原始请求数据 `data` 和已验证的字段 `validated` 注入到被装饰函数的kwargs中。
    """

    def decorator(f: F) -> F:
        @wraps(f)
        def decorated_function(*args: Any, **kwargs: Any) -> ApiResponse:
            # 1. 确保请求体是JSON
            data = RequestValidator.get_json_unwrap()
            # 2. 验证所有必需字段都存在
            validated_data = RequestValidator.extract_and_validate(
                data, *required_fields
            )
            # 3. 注入到kwargs并调用原函数
            kwargs["data"] = data
            kwargs["validated"] = validated_data
            return f(*args, **kwargs)

        return decorated_function  # type: ignore[return-value]

    return decorator
