import time
import logging
import threading
from collections import deque
from datetime import datetime

from rich.console import Console
from rich.layout import Layout
from rich.live import Live
from rich.panel import Panel
from rich.text import Text

# --- 从您的项目中导入 ---
from config.logging_config import setup_logging, get_log_buffer_handler
from app import main_app

# --- 配置 ---
MAX_LOG_LINES = 100
REFRESH_INTERVAL = 0.1

# --- 面板样式和分类规则 ---
PANELS_CONFIG = {
    "api": {
        "title": "[bold blue]API Logs (arcweb.api)[/bold blue]",
        "color": "blue",
        "logger_name": "arcweb.api",
    },
    "server": {
        "title": "[bold green]雷达服务器 Logs (arcweb.server)[/bold green]",
        "color": "green",
        "logger_name": "arcweb.server",
    },
    "simulator": {
        "title": "[bold magenta]模拟器 Logs (arcweb.simulator)[/bold magenta]",
        "color": "magenta",
        "logger_name": "arcweb.simulator",
    },
    "client": {
        "title": "[bold yellow]雷达客户端/雷达 Logs (arcweb.radar)[/bold yellow]",
        "color": "yellow",
        "logger_name": "arcweb.radar",
    },
    "worker": {
        "title": "[bold bright_cyan]Worker Logs (arcweb.worker)[/bold bright_cyan]",
        "color": "bright_cyan",
        "logger_name": "arcweb.worker",
    },
    "other": {
        "title": "[bold grey70]其他/未知来源 Logs[/bold grey70]",
        "color": "grey70",
        "logger_name": "",
    },
}

# --- UI 布局定义 ---
console = Console()
layout = Layout()
layout.split(Layout(name="header", size=3), Layout(ratio=1, name="main"))
layout["main"].split_row(Layout(name="left"), Layout(name="right"))
layout["left"].split_column(
    Layout(name="api"), Layout(name="simulator"), Layout(name="worker")
)
layout["right"].split_column(
    Layout(name="server"), Layout(name="client"), Layout(name="other")
)


def categorize_log_record(record: logging.LogRecord) -> str:
    for panel_key, config in PANELS_CONFIG.items():
        if panel_key == "other":
            continue
        if record.name.startswith(config["logger_name"]):
            return panel_key
    return "other"


def format_log_record(record: logging.LogRecord) -> str:
    dt = datetime.fromtimestamp(record.created)
    asctime = dt.strftime("%Y-%m-%d %H:%M:%S,%f")[:-3]
    message = record.getMessage()

    if record.name == "werkzeug":
        try:
            http_part_start = message.find('"')
            if http_part_start != -1:
                http_part = message[http_part_start:]
                return f"{asctime} - {record.levelname} - {http_part}"
        except IndexError:
            pass  # Fallback to default

    if message.lstrip().startswith("[") and "]" in message:
        return f"{asctime} - {record.levelname} - {message}"

    return f"{asctime} - {record.name} - {record.levelname} - {message}"


def create_log_panel(category_name: str, logs: deque) -> Panel:
    config = PANELS_CONFIG[category_name]
    log_text = Text("\n".join(logs), style="white", no_wrap=True)
    if not logs:
        log_text = Text("暂无日志 ...", style="dim")

    log_text.highlight_regex(r"INFO", "bold green")
    log_text.highlight_regex(r"DEBUG", "bold cyan")
    log_text.highlight_regex(r"WARNING", "bold yellow")
    log_text.highlight_regex(r"ERROR", "bold red")
    log_text.highlight_regex(r"CRITICAL", "bold white on red")

    return Panel(
        log_text,
        title=config["title"],
        border_style=config["color"],
        title_align="left",
    )


def main():
    """主函数，启动应用和监控仪表盘"""
    setup_logging()
    buffer_handler = get_log_buffer_handler()
    logs = {name: deque(maxlen=MAX_LOG_LINES) for name in PANELS_CONFIG}

    header_text = Text("实时雷达系统日志监控 (按 Ctrl+C 退出)", justify="center")
    layout["header"].update(Panel(header_text, style="bold blue"))

    app_thread = threading.Thread(target=main_app, daemon=True)
    app_thread.start()

    with Live(
        layout,
        console=console,
        screen=True,
        redirect_stderr=False,
        refresh_per_second=10,
    ) as live:
        try:
            # --- 核心逻辑修改：增量更新 ---
            processed_record_ids = set()  # 用于跟踪已处理的日志记录ID

            while app_thread.is_alive():
                # 从内存缓冲区获取所有当前的日志记录
                all_records = buffer_handler.get_records()
                updated_panels = set()

                # 找出新的、未被处理的日志记录
                new_records = [
                    rec for rec in all_records if id(rec) not in processed_record_ids
                ]

                if new_records:
                    for record in new_records:
                        category = categorize_log_record(record)
                        display_line = format_log_record(record)
                        logs[category].appendleft(display_line)
                        updated_panels.add(category)
                        processed_record_ids.add(id(record))

                    # 一次性更新所有变化的面板
                    for panel_name in updated_panels:
                        panel = create_log_panel(panel_name, logs[panel_name])
                        layout[panel_name].update(panel)

                # 维护 processed_record_ids 集合的大小，防止无限增长
                # 只保留当前缓冲区中存在的记录ID
                current_record_ids = {id(rec) for rec in all_records}
                processed_record_ids.intersection_update(current_record_ids)

                time.sleep(REFRESH_INTERVAL)

        except KeyboardInterrupt:
            live.stop()
            console.print("[bold red]监控已停止。[/bold red]")


if __name__ == "__main__":
    main()
