"""
验证框架使用示例
展示如何使用新的验证框架简化代码
"""

from flask import Blueprint, jsonify
from validators import (
    validate_request,
    with_database_doc,
    validate,
    db_helper,
    RequestValidator,
    ValidationError,
)
from flask_jwt_extended import jwt_required  # type: ignore
from utils import handle_api_exceptions, handle_database_exceptions
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)

# 创建示例蓝图
examples = Blueprint("examples", __name__)


# 示例1: 使用装饰器自动验证 - 最简洁的方式
@examples.route("/scene_coordinates_v2", methods=["POST"])
@jwt_required()
@handle_api_exceptions("获取场景坐标")
@handle_database_exceptions
@validate_request("scene_ID")  # 自动验证必需字段
@with_database_doc("scene", "scene_ID", "scene", "场景不存在")  # 自动查询文档
def get_scene_coordinates_v2(
    data: Dict[str, Any], validated: Dict[str, Any], scene: Dict[str, Any]  # type: ignore
):
    """获取场景坐标 - 简化版本"""

    # 使用链式验证器验证坐标字段
    coordinates = (
        validate(scene.get("coordinates"), "场景坐标")
        .required("场景坐标不存在")
        .not_empty("雷达未设置场景坐标")
        .raise_if_invalid()
    )

    return (
        jsonify(
            {
                "status": "success",
                "message": "雷达场景坐标加载成功",
                "data": coordinates,
            }
        ),
        200,
    )


# 示例2: 使用数据库助手 - 中等复杂度
@examples.route("/radar_host_v2", methods=["POST"])
@jwt_required()
@handle_api_exceptions("获取雷达主机")
@handle_database_exceptions
def get_radar_host_v2():
    """获取雷达主机 - 使用数据库助手"""
    try:
        # 验证请求数据
        data = RequestValidator.get_json_or_fail()

        # 验证radar_ID字段
        radar_id = (
            RequestValidator.validate_field(data, "radar_ID")
            .required()
            .not_empty()
            .raise_if_invalid()
        )

        # 使用数据库助手查询
        from app import db_base_data_radar

        helper = db_helper(db_base_data_radar)

        # 查找雷达文档并获取坐标字段
        coordinates = helper.find_field_or_fail(
            {"ID": radar_id}, "coordinates", "雷达坐标不存在"
        )

        # 验证坐标不为空
        if isinstance(coordinates, dict) and len(coordinates) == 0:  # type: ignore
            raise ValidationError("雷达未设置坐标", "COORDINATES_EMPTY", 500)

        return (
            jsonify(
                {
                    "status": "success",
                    "message": "雷达坐标加载成功",
                    "data": coordinates,
                }
            ),
            200,
        )

    except ValidationError as e:
        return e.to_response()


# 示例3: 手动使用链式验证器 - 最灵活的方式
@examples.route("/latest_image_v2", methods=["POST"])
@jwt_required()
@handle_api_exceptions("获取最新图像")
@handle_database_exceptions
def get_latest_image_v2():
    """获取最新图像 - 手动验证版本"""
    try:
        # 获取请求数据
        data = RequestValidator.get_json_or_fail()

        # 链式验证多个字段
        radar_id = (
            validate(data.get("radar_ID"), "雷达ID")
            .required()
            .not_empty()
            .min_length(1)
            .raise_if_invalid()
        )

        mission_id = (
            validate(data.get("mission_ID"), "任务ID")
            .required()
            .not_empty()
            .custom(lambda x: x.isdigit(), "任务ID必须是数字", "INVALID_MISSION_ID")
            .raise_if_invalid()
        )

        # 数据库操作
        from config.config import DatabaseConfig
        from app import app

        db_config = DatabaseConfig(db_name=radar_id)
        db_this_radar = db_config.init_app(app)

        # 数据库连接检查（实际上不会为None，但保留检查逻辑）
        # if db_this_radar is None:
        #     raise ValidationError("雷达数据库连接失败", "DB_CONNECTION_ERROR", 500)

        collection = db_this_radar[f"img_data_{mission_id}"]
        helper = db_helper(collection)

        # 查找最新图像记录
        base_data_doc = helper.find_one_or_fail(
            {"任务ID": int(mission_id)}, "未找到图像数据", "IMAGE_DATA_NOT_FOUND"
        )

        # 验证文件路径
        file_path = (
            validate(base_data_doc.get("road_xy"), "图像路径")
            .required("图像路径不存在")
            .not_empty("图像路径为空")
            .raise_if_invalid()
        )

        return (
            jsonify(
                {"status": "success", "message": f"http://127.0.0.1:5000/{file_path}"}
            ),
            200,
        )

    except ValidationError as e:
        return e.to_response()


# 示例4: 批量验证和复杂逻辑
@examples.route("/batch_validation_example", methods=["POST"])
@jwt_required()
@handle_api_exceptions("批量验证示例")
@handle_database_exceptions
def batch_validation_example():
    """展示批量验证和复杂验证逻辑"""
    try:
        data = RequestValidator.get_json_or_fail()

        # 批量验证多个字段
        validated_fields = RequestValidator.extract_fields(
            data, "scene_ID", "radar_IDs", "time_range"
        )

        scene_id = validated_fields["scene_ID"]
        radar_ids = validated_fields["radar_IDs"]
        time_range = validated_fields["time_range"]

        # 验证radar_IDs是列表且不为空
        (
            validate(radar_ids, "雷达ID列表")
            .custom(
                lambda x: isinstance(x, list), "雷达ID必须是列表", "INVALID_RADAR_IDS"
            )
            .not_empty("雷达ID列表不能为空")
            .raise_if_invalid()
        )

        # 验证时间范围
        (
            validate(time_range, "时间范围")
            .custom(
                lambda x: isinstance(x, dict),
                "时间范围必须是对象",
                "INVALID_TIME_RANGE",
            )
            .custom(
                lambda x: "start" in x and "end" in x,
                "时间范围必须包含start和end",
                "MISSING_TIME_FIELDS",
            )
            .raise_if_invalid()
        )

        # 验证场景存在
        from app import db_base_data_scene

        scene_helper = db_helper(db_base_data_scene)
        scene = scene_helper.find_one_or_fail(
            {"_id": scene_id}, "场景不存在", "SCENE_NOT_FOUND"
        )

        # 验证雷达存在
        from app import db_base_data_radar

        radar_helper = db_helper(db_base_data_radar)

        valid_radars = []
        for radar_id in radar_ids:
            try:
                radar = radar_helper.find_one_or_fail(
                    {"ID": radar_id}, f"雷达 {radar_id} 不存在", "RADAR_NOT_FOUND"
                )
                valid_radars.append(radar)  # type: ignore
            except ValidationError:
                # 记录警告但继续处理其他雷达
                logger.warning(f"雷达 {radar_id} 不存在，跳过处理")
                continue

        if not valid_radars:
            raise ValidationError("没有找到有效的雷达", "NO_VALID_RADARS", 404)

        return (
            jsonify(
                {
                    "status": "success",
                    "message": "批量验证成功",
                    "data": {
                        "scene": scene["name"] if "name" in scene else "未命名场景",
                        "valid_radar_count": len(valid_radars),  # type: ignore
                        "time_range": time_range,
                    },
                }
            ),
            200,
        )

    except ValidationError as e:
        return e.to_response()


# 示例5: 自定义验证器组合
def validate_coordinates(coords: Any) -> bool:
    """自定义坐标验证函数"""
    if not isinstance(coords, dict):
        return False
    required_keys = ["latitude", "longitude"]
    return all(
        key in coords and isinstance(coords[key], (int, float)) for key in required_keys
    )


@examples.route("/custom_validation_example", methods=["POST"])
@jwt_required()
@handle_api_exceptions("自定义验证示例")
@handle_database_exceptions
def custom_validation_example():
    """展示自定义验证器的使用"""
    try:
        data = RequestValidator.get_json_or_fail()

        # 使用自定义验证器
        coordinates = (
            validate(data.get("coordinates"), "坐标")
            .required()
            .custom(
                validate_coordinates,
                "坐标格式不正确，必须包含latitude和longitude",
                "INVALID_COORDINATES",
            )
            .raise_if_invalid()
        )

        # 验证坐标范围
        lat = coordinates["latitude"]
        lng = coordinates["longitude"]

        (
            validate(lat, "纬度")
            .custom(
                lambda x: -90 <= x <= 90, "纬度必须在-90到90之间", "INVALID_LATITUDE"
            )
            .raise_if_invalid()
        )

        (
            validate(lng, "经度")
            .custom(
                lambda x: -180 <= x <= 180,
                "经度必须在-180到180之间",
                "INVALID_LONGITUDE",
            )
            .raise_if_invalid()
        )

        return (
            jsonify(
                {"status": "success", "message": "坐标验证成功", "data": coordinates}
            ),
            200,
        )

    except ValidationError as e:
        return e.to_response()
