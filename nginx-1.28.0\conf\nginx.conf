# events 块是必须的，用于配置网络连接处理。
events {
    worker_connections 1024;
}

# http 块用于处理 HTTP 请求。
http {
    # 包含文件扩展名与文件类型的映射表
    include       mime.types;
    # 默认文件类型
    default_type  application/octet-stream;

    # --- 新增部分 ---
    # 定义一个名为 websocket_backend 的后端服务池。
    # 它指向我们之前创建的 Python WebSocket 服务器。
    upstream websocket_backend {
        server 127.0.0.1:8765; # 确保端口号与您 watchdog.py 中设置的一致
    }
    # --- 新增部分结束 ---

    server {
        # 监听 8000 端口
        listen 8000;
        server_name localhost;

        # --- 新增部分 ---
        # 当 URL 以 /ws/ 开头时，此规则生效 (用于 WebSocket)
        location /ws/ {
            # 将请求传递给上面定义的后端服务
            proxy_pass http://websocket_backend;

            # 以下是代理 WebSocket 的关键配置
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
            proxy_set_header Host $host;
            # 设置一个较长的超时时间，防止连接被意外断开
            proxy_read_timeout 86400s;
            proxy_send_timeout 86400s;
        }
        # --- 新增部分结束 ---


        # --- 您原有的 location 块 (已做语法修正) ---
        # 注意： 'location ./terrain_data/' 不是标准写法，已修正为 '/terrain_data/'
        # 它匹配的是 URL，例如 http://localhost:8000/terrain_data/some_file
        location /terrain_data/ {

            # 建议使用绝对路径代替相对路径，例如:
            # root /path/to/your/project;
            # 这里暂时保留您原来的逻辑，但请注意它依赖于 Nginx 的启动目录。
            root   ../;

            # 开启目录浏览
            autoindex on;

            # 跨域配置 (CORS) - 保持不变
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range' always;
            add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;

            if ($request_method = 'OPTIONS') {
                return 204;
            }
        }
    }
}