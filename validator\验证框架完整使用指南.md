# 验证框架完整使用指南

## 目录
1. [框架概述](#框架概述)
2. [安装和设置](#安装和设置)
3. [基础概念](#基础概念)
4. [链式验证器详解](#链式验证器详解)
5. [数据库操作助手](#数据库操作助手)
6. [异常处理机制](#异常处理机制)
7. [Flask集成](#flask集成)
8. [实战案例](#实战案例)
9. [最佳实践](#最佳实践)
10. [常见问题](#常见问题)

## 框架概述

这是一个高级验证和数据库操作框架，专为Flask应用和MongoDB设计。主要特性包括：

- **链式验证语法**：流畅的API设计，支持连续验证
- **统一异常处理**：自动捕获和格式化错误响应
- **数据库集成**：简化MongoDB CRUD操作
- **类型安全**：完整的TypeScript风格类型注解
- **Flask装饰器**：自动化请求验证和错误处理

## 安装和设置

### 依赖要求
```bash
pip install flask pymongo bson
```

### 基本设置
```python
from flask import Flask
from pymongo import MongoClient
from validation_framework import *

app = Flask(__name__)
mongo_client = MongoClient("mongodb://localhost:27017/")

# 配置日志
import logging
logging.basicConfig(level=logging.INFO)
```

## 基础概念

### 1. ValidationError - 统一异常类
```python
# 基本用法
raise ValidationError("用户名不能为空", "REQUIRED_FIELD", 400)

# 转换为HTTP响应
try:
    # 业务逻辑
    pass
except ValidationError as e:
    return e.to_response()  # 返回格式化的JSON响应
```

### 2. ValidationResult - 结果包装器
```python
# 创建结果对象
success_result = ValidationResult(value="hello")
error_result = ValidationResult(error=ValidationError("错误信息"))

# 检查结果
if result.is_valid:
    value = result.value
else:
    error = result.error

# 安全解包
value = result.unwrap()  # 成功返回值，失败抛异常
value = result.unwrap_or("默认值")  # 失败时返回默认值
```

## 链式验证器详解

### 基本用法
```python
# 创建验证器
validator = validate(user_input, "用户名")

# 链式验证
result = validate(username, "用户名")\
    .required()\
    .not_empty()\
    .min_length(3)\
    .max_length(20)\
    .result()

# 直接解包（推荐）
validated_username = validate(username, "用户名")\
    .required()\
    .min_length(3)\
    .unwrap()
```

### 所有验证方法

#### 1. required() - 必填验证
```python
# 检查值是否为None
username = validate(data.get("username"), "用户名")\
    .required("用户名是必填项")\
    .unwrap()
```

#### 2. not_empty() - 非空验证
```python
# 检查字符串、列表、字典是否为空
email = validate(user_email, "邮箱")\
    .required()\
    .not_empty("邮箱不能为空字符串")\
    .unwrap()

# 列表验证
tags = validate(tag_list, "标签列表")\
    .not_empty("至少需要一个标签")\
    .unwrap()
```

#### 3. min_length() / max_length() - 长度验证
```python
# 字符串长度
password = validate(pwd, "密码")\
    .required()\
    .min_length(8, "密码长度不能少于8位")\
    .max_length(50, "密码长度不能超过50位")\
    .unwrap()

# 列表长度
items = validate(item_list, "商品列表")\
    .min_length(1, "至少选择一个商品")\
    .max_length(10, "最多选择10个商品")\
    .unwrap()
```

#### 4. is_valid_object_id() - ObjectId验证
```python
# 验证MongoDB ObjectId格式
user_id = validate(request_id, "用户ID")\
    .required()\
    .is_valid_object_id("用户ID格式错误")\
    .unwrap()
```

#### 5. custom() - 自定义验证
```python
# 邮箱格式验证
email = validate(user_email, "邮箱")\
    .required()\
    .custom(
        lambda x: "@" in x and "." in x,
        "邮箱格式不正确",
        "INVALID_EMAIL_FORMAT"
    )\
    .unwrap()

# 年龄范围验证
age = validate(user_age, "年龄")\
    .required()\
    .custom(
        lambda x: isinstance(x, int) and 0 < x < 150,
        "年龄必须是1-149之间的整数",
        "INVALID_AGE"
    )\
    .unwrap()

# 枚举值验证
status = validate(order_status, "订单状态")\
    .required()\
    .custom(
        lambda x: x in ["pending", "paid", "shipped", "completed"],
        "订单状态不合法",
        "INVALID_STATUS"
    )\
    .unwrap()
```

### 复杂验证示例
```python
def validate_user_registration(data):
    """用户注册数据验证示例"""
    
    # 用户名验证
    username = validate(data.get("username"), "用户名")\
        .required()\
        .not_empty()\
        .min_length(3, "用户名至少3个字符")\
        .max_length(20, "用户名不能超过20个字符")\
        .custom(
            lambda x: x.isalnum(),
            "用户名只能包含字母和数字",
            "INVALID_USERNAME_FORMAT"
        )\
        .unwrap()
    
    # 邮箱验证
    email = validate(data.get("email"), "邮箱")\
        .required()\
        .not_empty()\
        .custom(
            lambda x: "@" in x and len(x.split("@")) == 2,
            "邮箱格式不正确"
        )\
        .unwrap()
    
    # 密码验证
    password = validate(data.get("password"), "密码")\
        .required()\
        .min_length(8, "密码至少8位")\
        .custom(
            lambda x: any(c.isupper() for c in x),
            "密码必须包含至少一个大写字母"
        )\
        .custom(
            lambda x: any(c.isdigit() for c in x),
            "密码必须包含至少一个数字"
        )\
        .unwrap()
    
    return {
        "username": username,
        "email": email,
        "password": password
    }
```

## 数据库操作助手

### 创建助手实例
```python
# 方式1：使用便捷函数
users_helper = db_helper(mongo_client, "myapp", "users")
orders_helper = db_helper(mongo_client, "myapp", "orders")

# 方式2：直接实例化
from pymongo import MongoClient
client = MongoClient()
collection = client["myapp"]["users"]
helper = DatabaseHelper(collection)
```

### 查询操作

#### 1. find_one_or_fail() - 查找单个文档
```python
# 基本用法
user = users_helper.find_one_or_fail(
    {"username": "john"},
    "用户不存在",
    "USER_NOT_FOUND"
)

# 带字段投影
user = users_helper.find_one_or_fail(
    {"_id": ObjectId(user_id)},
    projection={"password": 0}  # 排除密码字段
)
```

#### 2. find_many_or_empty() - 查找多个文档
```python
# 基本查询
users = users_helper.find_many_or_empty(
    {"status": "active"},
    limit=10,
    skip=0,
    sort=[("created_at", -1)]
)

# 分页查询
def get_users_page(page=1, per_page=10):
    skip = (page - 1) * per_page
    users = users_helper.find_many_or_empty(
        {},
        limit=per_page,
        skip=skip,
        sort=[("username", 1)]
    )
    total = users_helper.count_documents({})
    return {
        "users": users,
        "total": total,
        "page": page,
        "per_page": per_page
    }
```

#### 3. find_many_or_fail() - 查找多个文档（必须有结果）
```python
# 查找用户的所有订单
orders = orders_helper.find_many_or_fail(
    {"user_id": user_id},
    "该用户没有任何订单",
    "NO_ORDERS_FOUND"
)
```

#### 4. 存在性和计数
```python
# 检查用户名是否已存在
if users_helper.exists({"username": new_username}):
    raise ValidationError("用户名已存在", "USERNAME_EXISTS", 409)

# 统计活跃用户数
active_count = users_helper.count_documents({"status": "active"})
```

#### 5. 字段查询
```python
# 获取用户邮箱
email = users_helper.find_field_or_fail(
    {"_id": ObjectId(user_id)},
    "email",
    "用户邮箱不存在"
)

# 获取非空的用户标签列表
tags = users_helper.find_non_empty_field(
    {"_id": ObjectId(user_id)},
    "tags",
    "用户标签列表为空"
)
```

### 写入操作

#### 1. insert_one_and_return() - 插入单个文档
```python
# 创建新用户
new_user = users_helper.insert_one_and_return({
    "username": username,
    "email": email,
    "password": hashed_password,
    "created_at": datetime.utcnow(),
    "status": "active"
})

# 返回的文档包含_id字段
user_id = new_user["_id"]
```

#### 2. insert_many_and_return() - 批量插入
```python
# 批量创建订单项
order_items = [
    {"product_id": "prod1", "quantity": 2, "price": 29.99},
    {"product_id": "prod2", "quantity": 1, "price": 19.99}
]

inserted_items = orders_helper.insert_many_and_return(order_items)
for item in inserted_items:
    print(f"插入了商品: {item['_id']}")
```

### 更新操作

#### 1. update_one_and_return() - 更新单个文档
```python
# 更新用户信息并返回更新后的文档
updated_user = users_helper.update_one_and_return(
    {"_id": ObjectId(user_id)},
    {"$set": {"last_login": datetime.utcnow()}},
    return_updated=True
)

# 仅获取更新统计
update_result = users_helper.update_one_and_return(
    {"_id": ObjectId(user_id)},
    {"$inc": {"login_count": 1}},
    return_updated=False
)
print(f"修改了 {update_result['modified_count']} 个文档")
```

#### 2. update_many_and_count() - 批量更新
```python
# 批量更新用户状态
result = users_helper.update_many_and_count(
    {"last_login": {"$lt": datetime.utcnow() - timedelta(days=30)}},
    {"$set": {"status": "inactive"}}
)
print(f"匹配 {result['matched_count']} 个，修改 {result['modified_count']} 个")
```

### 删除操作

#### 1. delete_one_and_return() - 删除单个文档
```python
# 删除用户并返回被删除的文档
deleted_user = users_helper.delete_one_and_return(
    {"_id": ObjectId(user_id)}
)

if deleted_user:
    print(f"删除了用户: {deleted_user['username']}")
else:
    print("用户不存在")
```

#### 2. delete_many_and_count() - 批量删除
```python
# 删除过期的会话
deleted_count = orders_helper.delete_many_and_count(
    {"expires_at": {"$lt": datetime.utcnow()}},
    error_if_none_deleted=False
)
print(f"删除了 {deleted_count} 个过期会话")

# 要求至少删除一个
deleted_count = orders_helper.delete_many_and_count(
    {"status": "cancelled"},
    error_if_none_deleted=True
)
```

## 异常处理机制

### 1. handle_api_exceptions - 通用异常处理
```python
@handle_api_exceptions("用户管理")
def create_user():
    # 如果这里抛出 ValidationError，会自动转换为相应的HTTP响应
    # 如果抛出其他异常，会返回500错误
    username = validate(request.json.get("username"), "用户名").required().unwrap()
    # ... 业务逻辑
    return jsonify({"success": True}), 201
```

### 2. handle_database_exceptions - 数据库异常处理
```python
@handle_database_exceptions
def database_operation():
    # 自动处理各种MongoDB异常：
    # - ConnectionFailure -> 503 数据库连接失败
    # - DuplicateKeyError -> 409 数据重复
    # - WriteError -> 400 写入错误
    # 等等...
    return users_helper.insert_one_and_return(user_data)
```

### 3. 异常处理组合使用
```python
@handle_api_exceptions("创建用户")
@handle_database_exceptions
def create_user_endpoint():
    # 这个函数会自动处理验证错误、数据库错误和其他未知错误
    pass
```

## Flask集成

### 1. validate_request - 自动请求验证
```python
@app.route("/users", methods=["POST"])
@validate_request("username", "email", "password")
def create_user(data, validated):
    """
    data: 原始请求JSON数据
    validated: 已验证的必需字段数据
    """
    # 进一步验证
    username = validate(validated["username"], "用户名")\
        .min_length(3)\
        .max_length(20)\
        .unwrap()
    
    # 业务逻辑
    # ...
    
    return jsonify({"user_id": str(user["_id"])}), 201
```

### 2. 完整的Flask端点示例
```python
@app.route("/users/<user_id>", methods=["PUT"])
@handle_api_exceptions("更新用户")
@handle_database_exceptions
@validate_request("email")
def update_user(user_id, data, validated):
    # 验证用户ID格式
    user_id = validate(user_id, "用户ID").is_valid_object_id().unwrap()
    
    # 验证邮箱格式
    email = validate(validated["email"], "邮箱")\
        .custom(lambda x: "@" in x, "邮箱格式不正确")\
        .unwrap()
    
    # 检查用户是否存在
    users_helper = db_helper(mongo_client, "myapp", "users")
    user = users_helper.find_one_or_fail(
        {"_id": ObjectId(user_id)},
        "用户不存在"
    )
    
    # 检查邮箱是否已被其他用户使用
    if users_helper.exists({"email": email, "_id": {"$ne": ObjectId(user_id)}}):
        raise ValidationError("邮箱已被使用", "EMAIL_EXISTS", 409)
    
    # 更新用户
    updated_user = users_helper.update_one_and_return(
        {"_id": ObjectId(user_id)},
        {"$set": {"email": email, "updated_at": datetime.utcnow()}}
    )
    
    return jsonify({
        "message": "用户更新成功",
        "user": {
            "id": str(updated_user["_id"]),
            "username": updated_user["username"],
            "email": updated_user["email"]
        }
    })
```

## 实战案例

### 案例1：用户管理系统

#### 用户注册
```python
@app.route("/register", methods=["POST"])
@handle_api_exceptions("用户注册")
@handle_database_exceptions
@validate_request("username", "email", "password")
def register(data, validated):
    # 验证用户名
    username = validate(validated["username"], "用户名")\
        .not_empty()\
        .min_length(3, "用户名至少3个字符")\
        .max_length(20, "用户名不能超过20个字符")\
        .custom(lambda x: x.isalnum(), "用户名只能包含字母和数字")\
        .unwrap()
    
    # 验证邮箱
    email = validate(validated["email"], "邮箱")\
        .not_empty()\
        .custom(
            lambda x: re.match(r'^[^@]+@[^@]+\.[^@]+$', x),
            "邮箱格式不正确"
        )\
        .unwrap()
    
    # 验证密码
    password = validate(validated["password"], "密码")\
        .min_length(8, "密码至少8位")\
        .custom(lambda x: any(c.isupper() for c in x), "密码必须包含大写字母")\
        .custom(lambda x: any(c.isdigit() for c in x), "密码必须包含数字")\
        .unwrap()
    
    # 数据库操作
    users_helper = db_helper(mongo_client, "myapp", "users")
    
    # 检查用户名和邮箱唯一性
    if users_helper.exists({"username": username}):
        raise ValidationError("用户名已存在", "USERNAME_EXISTS", 409)
    
    if users_helper.exists({"email": email}):
        raise ValidationError("邮箱已被注册", "EMAIL_EXISTS", 409)
    
    # 创建用户
    user = users_helper.insert_one_and_return({
        "username": username,
        "email": email,
        "password": hash_password(password),
        "created_at": datetime.utcnow(),
        "status": "active",
        "profile": {
            "avatar": None,
            "bio": "",
            "preferences": {}
        }
    })
    
    return jsonify({
        "message": "注册成功",
        "user_id": str(user["_id"])
    }), 201
```

#### 用户登录
```python
@app.route("/login", methods=["POST"])
@handle_api_exceptions("用户登录")
@handle_database_exceptions
@validate_request("username", "password")
def login(data, validated):
    username = validate(validated["username"], "用户名").required().unwrap()
    password = validate(validated["password"], "密码").required().unwrap()
    
    users_helper = db_helper(mongo_client, "myapp", "users")
    
    # 查找用户
    user = users_helper.find_one_or_fail(
        {"username": username},
        "用户名不存在",
        "USER_NOT_FOUND",
        projection={"password": 1, "status": 1, "username": 1}
    )
    
    # 验证密码
    if not verify_password(password, user["password"]):
        raise ValidationError("密码错误", "INVALID_PASSWORD", 401)
    
    # 检查用户状态
    if user["status"] != "active":
        raise ValidationError("账户已被禁用", "ACCOUNT_DISABLED", 403)
    
    # 更新最后登录时间
    users_helper.update_one_and_return(
        {"_id": user["_id"]},
        {"$set": {"last_login": datetime.utcnow()}},
        return_updated=False
    )
    
    # 生成JWT token
    token = generate_jwt_token(str(user["_id"]))
    
    return jsonify({
        "message": "登录成功",
        "token": token,
        "user": {
            "id": str(user["_id"]),
            "username": user["username"]
        }
    })
```

### 案例2：电商订单系统

#### 创建订单
```python
@app.route("/orders", methods=["POST"])
@handle_api_exceptions("创建订单")
@handle_database_exceptions
@validate_request("items", "shipping_address")
def create_order(data, validated):
    # 验证订单项
    items = validate(validated["items"], "订单项")\
        .not_empty("订单项不能为空")\
        .custom(
            lambda x: isinstance(x, list) and all(
                isinstance(item, dict) and 
                "product_id" in item and 
                "quantity" in item
                for item in x
            ),
            "订单项格式不正确"
        )\
        .unwrap()
    
    # 验证每个订单项
    validated_items = []
    for item in items:
        product_id = validate(item.get("product_id"), "商品ID")\
            .required()\
            .is_valid_object_id()\
            .unwrap()
        
        quantity = validate(item.get("quantity"), "数量")\
            .required()\
            .custom(
                lambda x: isinstance(x, int) and x > 0,
                "数量必须是正整数"
            )\
            .unwrap()
        
        validated_items.append({
            "product_id": ObjectId(product_id),
            "quantity": quantity
        })
    
    # 验证收货地址
    address = validate(validated["shipping_address"], "收货地址")\
        .custom(
            lambda x: isinstance(x, dict) and all(
                key in x for key in ["name", "phone", "address"]
            ),
            "收货地址格式不正确"
        )\
        .unwrap()
    
    # 数据库操作
    products_helper = db_helper(mongo_client, "myapp", "products")
    orders_helper = db_helper(mongo_client, "myapp", "orders")
    
    # 验证商品存在性和库存
    order_total = 0
    final_items = []
    
    for item in validated_items:
        product = products_helper.find_one_or_fail(
            {"_id": item["product_id"]},
            f"商品不存在",
            "PRODUCT_NOT_FOUND"
        )
        
        # 检查库存
        if product["stock"] < item["quantity"]:
            raise ValidationError(
                f"商品 {product['name']} 库存不足",
                "INSUFFICIENT_STOCK",
                400
            )
        
        item_total = product["price"] * item["quantity"]
        order_total += item_total
        
        final_items.append({
            "product_id": item["product_id"],
            "product_name": product["name"],
            "price": product["price"],
            "quantity": item["quantity"],
            "subtotal": item_total
        })
    
    # 创建订单
    order = orders_helper.insert_one_and_return({
        "user_id": ObjectId(get_current_user_id()),  # 从JWT获取
        "items": final_items,
        "total_amount": order_total,
        "shipping_address": address,
        "status": "pending",
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow()
    })
    
    # 更新商品库存
    for item in validated_items:
        products_helper.update_one_and_return(
            {"_id": item["product_id"]},
            {"$inc": {"stock": -item["quantity"]}},
            return_updated=False
        )
    
    return jsonify({
        "message": "订单创建成功",
        "order_id": str(order["_id"]),
        "total_amount": order_total
    }), 201
```

### 案例3：文件上传验证
```python
@app.route("/upload", methods=["POST"])
@handle_api_exceptions("文件上传")
def upload_file():
    # 检查文件是否存在
    if 'file' not in request.files:
        raise ValidationError("没有上传文件", "NO_FILE", 400)
    
    file = request.files['file']
    
    # 验证文件名
    filename = validate(file.filename, "文件名")\
        .required("文件名不能为空")\
        .custom(
            lambda x: '.' in x and x.rsplit('.', 1)[1].lower() in ['jpg', 'png', 'pdf'],
            "只支持JPG、PNG、PDF格式文件"
        )\
        .unwrap()
    
    # 验证文件大小
    file.seek(0, 2)  # 移动到文件末尾
    file_size = file.tell()
    file.seek(0)  # 回到文件开头
    
    validate(file_size, "文件大小")\
        .custom(
            lambda x: x <= 5 * 1024 * 1024,  # 5MB
            "文件大小不能超过5MB"
        )\
        .unwrap()
    
    # 保存文件逻辑...
    
    return jsonify({"message": "文件上传成功"}), 200
```

## 最佳实践

### 1. 验证器使用原则

#### 早期验证
```python
# ✅ 好的做法：在函数入口处立即验证
def update_user_profile(user_id, profile_data):
    user_id = validate(user_id, "用户ID").is_valid_object_id().unwrap()
    name = validate(profile_data.get("name"), "姓名").required().unwrap()
    # ... 业务逻辑
```

#### 链式验证的合理分组
```python
# ✅ 好的做法：逻辑相关的验证放在一起
password = validate(raw_password, "密码")\
    .required()\
    .min_length(8)\
    .max_length(128)\
    .unwrap()

# 复杂验证单独处理
validate(password, "密码")\
    .custom(has_uppercase, "必须包含大写字母")\
    .custom(has_digit, "必须包含数字")\
    .custom(has_special_char, "必须包含特殊字符")\
    .unwrap()
```

#### 自定义验证函数
```python
# 定义可复用的验证函数
def is_valid_email(email):
    return re.match(r'^[^@]+@[^@]+\.[^@]+$', email) is not None

def is_strong_password(password):
    return (len(password) >= 8 and 
            any(c.isupper() for c in password) and
            any(c.isdigit() for c in password))

# 使用
email = validate(user_email, "邮箱")\
    .required()\
    .custom(is_valid_email, "邮箱格式不正确")\
    .unwrap()
```

### 2. 错误处理策略

#### 统一错误响应格式
```python
# 自定义错误码常量
class ErrorCodes:
    VALIDATION_ERROR = "VALIDATION_ERROR"
    USER_NOT_FOUND = "USER_NOT_FOUND"
    INSUFFICIENT_PERMISSIONS = "INSUFFICIENT_PERMISSIONS"
    # ... 更多错误码

# 使用统一的错误码
raise ValidationError("用户不存在", ErrorCodes.USER_NOT_FOUND, 404)
```

#### 多语言错误消息
```python
ERROR_MESSAGES = {
    "zh": {
        "REQUIRED_FIELD": "{field}不能为空",
        "INVALID_EMAIL": "邮箱格式不正确"
    },
    "en": {
        "REQUIRED_FIELD": "{field} is required",
        "INVALID_EMAIL": "Invalid email format"
    }
}

def get_error_message(code, field="", lang="zh"):
    return ERROR_MESSAGES[lang][code].format(field=field)

# 使用
username = validate(data.get("username"), "用户名")\
    .required(get_error_message("REQUIRED_FIELD", "用户名"))\
    .unwrap()
```

### 3. 数据库操作优化

#### 事务处理
```python
from pymongo import MongoClient

def transfer_points(from_user_id, to_user_id, points):
    """积分转账示例 - 需要事务处理"""
    client = MongoClient()
    
    with client.start_session() as session:
        with session.start_transaction():
            users_helper = db_helper(client, "myapp", "users")
            
            # 验证转出用户
            from_user = users_helper.find_one_or_fail(
                {"_id": ObjectId(from_user_id)},
                "转出用户不存在"
            )
            
            # 检查余额
            if from_user.get("points", 0) < points:
                raise ValidationError("积分余额不足", "INSUFFICIENT_POINTS", 400)
            
            # 扣除积分
            users_helper.update_one_and_return(
                {"_id": ObjectId(from_user_id)},
                {"$inc": {"points": -points}},
                return_updated=False
            )
            
            # 增加积分
            users_helper.update_one_and_return(
                {"_id": ObjectId(to_user_id)},
                {"$inc": {"points": points}},
                return_updated=False
            )
            
            # 记录转账历史
            history_helper = db_helper(client, "myapp", "point_history")
            history_helper.insert_one_and_return({
                "from_user_id": ObjectId(from_user_id),
                "to_user_id": ObjectId(to_user_id),
                "points": points,
                "type": "transfer",
                "created_at": datetime.utcnow()
            })
```

#### 批量操作优化
```python
def batch_update_user_status(user_ids, new_status):
    """批量更新用户状态"""
    # 验证用户ID列表
    validated_ids = []
    for user_id in user_ids:
        validated_id = validate(user_id, "用户ID").is_valid_object_id().unwrap()
        validated_ids.append(ObjectId(validated_id))
    
    # 验证状态值
    status = validate(new_status, "状态")\
        .custom(
            lambda x: x in ["active", "inactive", "banned"],
            "状态值不合法"
        )\
        .unwrap()
    
    users_helper = db_helper(mongo_client, "myapp", "users")
    
    # 批量更新
    result = users_helper.update_many_and_count(
        {"_id": {"$in": validated_ids}},
        {"$set": {"status": status, "updated_at": datetime.utcnow()}}
    )
    
    return {
        "requested": len(validated_ids),
        "matched": result["matched_count"],
        "modified": result["modified_count"]
    }
```

### 4. 性能优化建议

#### 索引策略
```python
# 为常用查询字段创建索引
def create_indexes():
    users_collection = mongo_client["myapp"]["users"]
    orders_collection = mongo_client["myapp"]["orders"]
    
    # 用户表索引
    users_collection.create_index("username", unique=True)
    users_collection.create_index("email", unique=True)
    users_collection.create_index([("status", 1), ("created_at", -1)])
    
    # 订单表索引
    orders_collection.create_index("user_id")
    orders_collection.create_index([("status", 1), ("created_at", -1)])
    orders_collection.create_index("order_number", unique=True)
```

#### 查询优化
```python
# ✅ 好的做法：使用投影减少数据传输
def get_user_list():
    users_helper = db_helper(mongo_client, "myapp", "users")
    return users_helper.find_many_or_empty(
        {"status": "active"},
        projection={"username": 1, "email": 1, "created_at": 1},  # 只获取需要的字段
        limit=50,
        sort=[("created_at", -1)]
    )

# ✅ 好的做法：使用聚合管道进行复杂查询
def get_user_order_summary(user_id):
    user_id = validate(user_id, "用户ID").is_valid_object_id().unwrap()
    
    orders_collection = mongo_client["myapp"]["orders"]
    pipeline = [
        {"$match": {"user_id": ObjectId(user_id)}},
        {"$group": {
            "_id": "$status",
            "count": {"$sum": 1},
            "total_amount": {"$sum": "$total_amount"}
        }},
        {"$sort": {"_id": 1}}
    ]
    
    return list(orders_collection.aggregate(pipeline))
```

## 高级特性

### 1. 条件验证
```python
def validate_user_update(data, user_role):
    """根据用户角色进行不同的验证"""
    validated_data = {}
    
    # 普通字段验证
    if "email" in data:
        validated_data["email"] = validate(data["email"], "邮箱")\
            .custom(is_valid_email, "邮箱格式不正确")\
            .unwrap()
    
    # 管理员才能修改的字段
    if "role" in data:
        if user_role != "admin":
            raise ValidationError("无权限修改用户角色", "INSUFFICIENT_PERMISSIONS", 403)
        
        validated_data["role"] = validate(data["role"], "角色")\
            .custom(
                lambda x: x in ["user", "admin", "moderator"],
                "角色值不合法"
            )\
            .unwrap()
    
    return validated_data
```

### 2. 嵌套对象验证
```python
def validate_user_profile(profile_data):
    """验证嵌套的用户资料对象"""
    # 基本信息验证
    basic_info = validate(profile_data.get("basic_info"), "基本信息")\
        .required("基本信息不能为空")\
        .custom(
            lambda x: isinstance(x, dict),
            "基本信息格式错误"
        )\
        .unwrap()
    
    # 验证嵌套字段
    name = validate(basic_info.get("name"), "姓名")\
        .required()\
        .min_length(2, "姓名至少2个字符")\
        .max_length(50, "姓名不能超过50个字符")\
        .unwrap()
    
    age = validate(basic_info.get("age"), "年龄")\
        .custom(
            lambda x: isinstance(x, int) and 0 < x < 150,
            "年龄必须是1-149之间的整数"
        )\
        .unwrap()
    
    # 可选的联系信息
    contact_info = {}
    if "contact_info" in profile_data:
        contact_data = validate(profile_data["contact_info"], "联系信息")\
            .custom(lambda x: isinstance(x, dict), "联系信息格式错误")\
            .unwrap()
        
        if "phone" in contact_data:
            contact_info["phone"] = validate(contact_data["phone"], "电话")\
                .custom(
                    lambda x: re.match(r'^1[3-9]\d{9}$', x),
                    "手机号格式不正确"
                )\
                .unwrap()
    
    return {
        "basic_info": {
            "name": name,
            "age": age
        },
        "contact_info": contact_info
    }
```

### 3. 自定义装饰器
```python
from functools import wraps

def require_admin(f):
    """要求管理员权限的装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user_role = get_current_user_role()  # 从JWT或session获取
        if user_role != "admin":
            raise ValidationError("需要管理员权限", "ADMIN_REQUIRED", 403)
        return f(*args, **kwargs)
    return decorated_function

def validate_json_schema(schema):
    """JSON Schema验证装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                import jsonschema
                jsonschema.validate(request.json, schema)
            except jsonschema.ValidationError as e:
                raise ValidationError(f"数据格式错误: {e.message}", "SCHEMA_ERROR", 400)
            return f(*args, **kwargs)
        return decorated_function
    return decorator

# 使用示例
user_schema = {
    "type": "object",
    "properties": {
        "username": {"type": "string", "minLength": 3},
        "email": {"type": "string", "format": "email"}
    },
    "required": ["username", "email"]
}

@app.route("/admin/users", methods=["POST"])
@require_admin
@validate_json_schema(user_schema)
@handle_api_exceptions("创建用户")
def admin_create_user():
    # 这里的数据已经通过JSON Schema验证
    pass
```

## 测试策略

### 1. 单元测试
```python
import unittest
from unittest.mock import patch, MagicMock
from validation_framework import validate, ValidationError

class TestValidationFramework(unittest.TestCase):
    
    def test_required_validation(self):
        """测试必填验证"""
        # 正常情况
        result = validate("test", "字段").required().unwrap()
        self.assertEqual(result, "test")
        
        # 异常情况
        with self.assertRaises(ValidationError) as cm:
            validate(None, "字段").required().unwrap()
        
        self.assertEqual(cm.exception.code, "REQUIRED_FIELD")
        self.assertEqual(cm.exception.status_code, 400)
    
    def test_chain_validation(self):
        """测试链式验证"""
        # 成功的链式验证
        result = validate("hello", "测试")\
            .required()\
            .not_empty()\
            .min_length(3)\
            .max_length(10)\
            .unwrap()
        
        self.assertEqual(result, "hello")
        
        # 失败的链式验证
        with self.assertRaises(ValidationError):
            validate("hi", "测试")\
                .required()\
                .min_length(5)\
                .unwrap()
    
    def test_custom_validation(self):
        """测试自定义验证"""
        # 成功的自定义验证
        result = validate("<EMAIL>", "邮箱")\
            .custom(lambda x: "@" in x, "邮箱格式错误")\
            .unwrap()
        
        self.assertEqual(result, "<EMAIL>")
        
        # 失败的自定义验证
        with self.assertRaises(ValidationError) as cm:
            validate("invalid-email", "邮箱")\
                .custom(lambda x: "@" in x, "邮箱格式错误", "INVALID_EMAIL")\
                .unwrap()
        
        self.assertEqual(cm.exception.code, "INVALID_EMAIL")

class TestDatabaseHelper(unittest.TestCase):
    
    def setUp(self):
        """测试准备"""
        self.mock_collection = MagicMock()
        self.helper = DatabaseHelper(self.mock_collection)
    
    def test_find_one_or_fail_success(self):
        """测试成功查找单个文档"""
        # 模拟数据库返回
        expected_doc = {"_id": "123", "name": "test"}
        self.mock_collection.find_one.return_value = expected_doc
        
        result = self.helper.find_one_or_fail({"name": "test"})
        
        self.assertEqual(result, expected_doc)
        self.mock_collection.find_one.assert_called_once_with({"name": "test"}, None)
    
    def test_find_one_or_fail_not_found(self):
        """测试文档不存在的情况"""
        self.mock_collection.find_one.return_value = None
        
        with self.assertRaises(ValidationError) as cm:
            self.helper.find_one_or_fail({"name": "test"}, "文档不存在")
        
        self.assertEqual(cm.exception.message, "文档不存在")
        self.assertEqual(cm.exception.status_code, 404)
    
    @patch('validation_framework.ObjectId')
    def test_insert_one_and_return(self, mock_objectid):
        """测试插入文档"""
        # 模拟ObjectId
        mock_id = MagicMock()
        mock_objectid.return_value = mock_id
        
        # 模拟插入结果
        self.mock_collection.insert_one.return_value.inserted_id = mock_id
        
        doc_to_insert = {"name": "test"}
        result = self.helper.insert_one_and_return(doc_to_insert)
        
        # 验证返回的文档包含_id
        expected_result = {"name": "test", "_id": mock_id}
        self.assertEqual(result, expected_result)
```

### 2. 集成测试
```python
import pytest
from flask import Flask
from pymongo import MongoClient
from validation_framework import *

@pytest.fixture
def app():
    """创建测试Flask应用"""
    app = Flask(__name__)
    app.config["TESTING"] = True
    
    # 注册测试路由
    @app.route("/test-validation", methods=["POST"])
    @handle_api_exceptions("测试验证")
    @validate_request("username", "email")
    def test_validation(data, validated):
        username = validate(validated["username"], "用户名")\
            .min_length(3)\
            .unwrap()
        return {"message": "success", "username": username}
    
    return app

@pytest.fixture
def client(app):
    """创建测试客户端"""
    return app.test_client()

@pytest.fixture
def mongo_client():
    """创建测试MongoDB客户端"""
    client = MongoClient("mongodb://localhost:27017/")
    # 使用测试数据库
    test_db = client["test_validation_framework"]
    yield client
    # 清理测试数据
    client.drop_database("test_validation_framework")

def test_validation_success(client):
    """测试验证成功的情况"""
    response = client.post("/test-validation", json={
        "username": "testuser",
        "email": "<EMAIL>"
    })
    
    assert response.status_code == 200
    data = response.get_json()
    assert data["message"] == "success"
    assert data["username"] == "testuser"

def test_validation_failure(client):
    """测试验证失败的情况"""
    response = client.post("/test-validation", json={
        "username": "ab",  # 太短
        "email": "<EMAIL>"
    })
    
    assert response.status_code == 400
    data = response.get_json()
    assert "error" in data
    assert "用户名" in data["error"]["message"]

def test_missing_required_field(client):
    """测试缺失必填字段"""
    response = client.post("/test-validation", json={
        "username": "testuser"
        # 缺少email字段
    })
    
    assert response.status_code == 400
    data = response.get_json()
    assert "email" in data["error"]["message"]

def test_database_operations(mongo_client):
    """测试数据库操作"""
    helper = db_helper(mongo_client, "test_validation_framework", "users")
    
    # 测试插入
    user = helper.insert_one_and_return({
        "username": "testuser",
        "email": "<EMAIL>"
    })
    assert "_id" in user
    assert user["username"] == "testuser"
    
    # 测试查找
    found_user = helper.find_one_or_fail({"username": "testuser"})
    assert found_user["_id"] == user["_id"]
    
    # 测试更新
    updated_user = helper.update_one_and_return(
        {"_id": user["_id"]},
        {"$set": {"email": "<EMAIL>"}},
        return_updated=True
    )
    assert updated_user["email"] == "<EMAIL>"
    
    # 测试删除
    deleted_user = helper.delete_one_and_return({"_id": user["_id"]})
    assert deleted_user["_id"] == user["_id"]
    
    # 验证已删除
    with pytest.raises(ValidationError):
        helper.find_one_or_fail({"_id": user["_id"]})
```

## 常见问题

### 1. 验证相关问题

**Q: 如何处理可选字段的验证？**
```python
# ❌ 错误做法
age = validate(data.get("age"), "年龄").required().unwrap()  # 可选字段不应该required

# ✅ 正确做法
age = None
if "age" in data and data["age"] is not None:
    age = validate(data["age"], "年龄")\
        .custom(lambda x: isinstance(x, int) and x > 0, "年龄必须是正整数")\
        .unwrap()

# 或者使用条件验证
def validate_optional_age(value):
    if value is None:
        return None
    return validate(value, "年龄")\
        .custom(lambda x: isinstance(x, int) and x > 0, "年龄必须是正整数")\
        .unwrap()

age = validate_optional_age(data.get("age"))
```

**Q: 如何处理数组中每个元素的验证？**
```python
def validate_tag_list(tags):
    """验证标签列表"""
    # 先验证列表本身
    tags = validate(tags, "标签列表")\
        .required()\
        .not_empty("至少需要一个标签")\
        .max_length(10, "最多10个标签")\
        .unwrap()
    
    # 验证每个标签
    validated_tags = []
    for i, tag in enumerate(tags):
        validated_tag = validate(tag, f"标签[{i}]")\
            .required()\
            .not_empty()\
            .max_length(20, "标签长度不能超过20个字符")\
            .custom(
                lambda x: x.replace(" ", "").isalnum(),
                "标签只能包含字母、数字和空格"
            )\
            .unwrap()
        validated_tags.append(validated_tag.strip())
    
    return validated_tags
```

**Q: 如何实现跨字段验证？**
```python
def validate_password_change(data):
    """密码修改验证 - 需要验证新密码与确认密码一致"""
    old_password = validate(data.get("old_password"), "原密码").required().unwrap()
    new_password = validate(data.get("new_password"), "新密码")\
        .required()\
        .min_length(8)\
        .unwrap()
    confirm_password = validate(data.get("confirm_password"), "确认密码").required().unwrap()
    
    # 跨字段验证
    if new_password != confirm_password:
        raise ValidationError("新密码与确认密码不一致", "PASSWORD_MISMATCH", 400)
    
    if old_password == new_password:
        raise ValidationError("新密码不能与原密码相同", "SAME_PASSWORD", 400)
    
    return {
        "old_password": old_password,
        "new_password": new_password
    }
```

### 2. 数据库相关问题

**Q: 如何处理大量数据的分页查询？**
```python
def get_paginated_users(page=1, per_page=20, filters=None):
    """分页获取用户列表"""
    page = validate(page, "页码")\
        .custom(lambda x: isinstance(x, int) and x > 0, "页码必须是正整数")\
        .unwrap()
    
    per_page = validate(per_page, "每页数量")\
        .custom(lambda x: isinstance(x, int) and 1 <= x <= 100, "每页数量必须在1-100之间")\
        .unwrap()
    
    users_helper = db_helper(mongo_client, "myapp", "users")
    
    # 构建查询条件
    query = filters or {}
    
    # 计算跳过的记录数
    skip = (page - 1) * per_page
    
    # 获取数据和总数
    users = users_helper.find_many_or_empty(
        query,
        limit=per_page,
        skip=skip,
        sort=[("created_at", -1)],
        projection={"password": 0}  # 排除敏感字段
    )
    
    total = users_helper.count_documents(query)
    
    return {
        "users": users,
        "pagination": {
            "page": page,
            "per_page": per_page,
            "total": total,
            "pages": (total + per_page - 1) // per_page,
            "has_next": skip + per_page < total,
            "has_prev": page > 1
        }
    }
```

**Q: 如何处理数据库连接异常？**
```python
import pymongo
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError

@handle_database_exceptions
def robust_database_operation():
    """具有重试机制的数据库操作"""
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            users_helper = db_helper(mongo_client, "myapp", "users")
            return users_helper.find_many_or_empty({"status": "active"})
        
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            retry_count += 1
            if retry_count >= max_retries:
                raise ValidationError(
                    "数据库连接失败，请稍后重试",
                    "DATABASE_CONNECTION_ERROR",
                    503
                )
            
            # 等待后重试
            import time
            time.sleep(1 * retry_count)  # 递增等待时间
```

### 3. 性能相关问题

**Q: 如何优化大量验证的性能？**
```python
# ❌ 低效做法：每次都重新编译正则表达式
def validate_email_slow(email):
    import re
    return validate(email, "邮箱")\
        .custom(
            lambda x: re.match(r'^[^@]+@[^@]+\.[^@]+$', x),  # 每次都编译
            "邮箱格式不正确"
        )\
        .unwrap()

# ✅ 高效做法：预编译正则表达式
import re
EMAIL_PATTERN = re.compile(r'^[^@]+@[^@]+\.[^@]+$')

def validate_email_fast(email):
    return validate(email, "邮箱")\
        .custom(
            lambda x: EMAIL_PATTERN.match(x) is not None,
            "邮箱格式不正确"
        )\
        .unwrap()

# 更好的做法：定义验证函数库
class Validators:
    EMAIL_PATTERN = re.compile(r'^[^@]+@[^@]+\.[^@]+$')
    PHONE_PATTERN = re.compile(r'^1[3-9]\d{9}$')
    
    @staticmethod
    def is_valid_email(email):
        return Validators.EMAIL_PATTERN.match(email) is not None
    
    @staticmethod
    def is_valid_phone(phone):
        return Validators.PHONE_PATTERN.match(phone) is not None

# 使用
email = validate(user_email, "邮箱")\
    .custom(Validators.is_valid_email, "邮箱格式不正确")\
    .unwrap()
```

**Q: 如何处理大文件上传的验证？**
```python
@app.route("/upload-large", methods=["POST"])
@handle_api_exceptions("大文件上传")
def upload_large_file():
    """大文件上传处理"""
    MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
    
    if 'file' not in request.files:
        raise ValidationError("没有上传文件", "NO_FILE", 400)
    
    file = request.files['file']
    
    # 检查Content-Length头部
    content_length = request.content_length
    if content_length and content_length > MAX_FILE_SIZE:
        raise ValidationError(
            f"文件大小超过限制({MAX_FILE_SIZE // 1024 // 1024}MB)",
            "FILE_TOO_LARGE",
            413
        )
    
    # 流式验证文件大小
    total_size = 0
    chunk_size = 8192
    
    # 临时保存文件
    import tempfile
    with tempfile.NamedTemporaryFile() as temp_file:
        while True:
            chunk = file.stream.read(chunk_size)
            if not chunk:
                break
            
            total_size += len(chunk)
            if total_size > MAX_FILE_SIZE:
                raise ValidationError(
                    "文件大小超过限制",
                    "FILE_TOO_LARGE",
                    413
                )
            
            temp_file.write(chunk)
        
        # 验证文件类型（通过文件头）
        temp_file.seek(0)
        file_header = temp_file.read(8)
        
        # 简单的文件类型检测
        if not is_valid_file_type(file_header, file.filename):
            raise ValidationError("不支持的文件类型", "INVALID_FILE_TYPE", 400)
        
        # 处理文件...
        return jsonify({"message": "文件上传成功", "size": total_size})

def is_valid_file_type(file_header, filename):
    """通过文件头检测文件类型"""
    file_signatures = {
        b'\x89PNG\r\n\x1a\n': ['png'],
        b'\xff\xd8\xff': ['jpg', 'jpeg'],
        b'%PDF': ['pdf'],
        b'PK': ['zip', 'docx', 'xlsx']
    }
    
    extension = filename.rsplit('.', 1)[-1].lower() if '.' in filename else ''
    
    for signature, extensions in file_signatures.items():
        if file_header.startswith(signature) and extension in extensions:
            return True
    
    return False
```

## 部署和监控

### 1. 生产环境配置
```python
import logging
from logging.handlers import RotatingFileHandler

# 配置日志
def setup_logging(app):
    if not app.debug:
        # 文件日志
        file_handler = RotatingFileHandler(
            'logs/validation_framework.log',
            maxBytes=10240000,  # 10MB
            backupCount=10
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        
        app.logger.setLevel(logging.INFO)
        app.logger.info('验证框架启动')

# 性能监控
def log_validation_performance(func):
    """验证性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            
            if duration > 0.1:  # 超过100ms记录警告
                logging.warning(f"验证耗时过长: {func.__name__} - {duration:.3f}s")
            
            return result
        except Exception as e:
            duration = time.time() - start_time
            logging.error(f"验证失败: {func.__name__} - {duration:.3f}s - {str(e)}")
            raise
    
    return wrapper
```

### 2. 监控指标
```python
from collections import defaultdict
import threading

class ValidationMetrics:
    """验证指标收集器"""
    def __init__(self):
        self.metrics = defaultdict(lambda: {
            'total_count': 0,
            'error_count': 0,
            'total_time': 0.0
        })
        self.lock = threading.Lock()
    
    def record_validation(self, field_name, success, duration):
        """记录验证指标"""
        with self.lock:
            self.metrics[field_name]['total_count'] += 1
            if not success:
                self.metrics[field_name]['error_count'] += 1
            self.metrics[field_name]['total_time'] += duration
    
    def get_metrics(self):
        """获取指标统计"""
        with self.lock:
            result = {}
            for field, data in self.metrics.items():
                result[field] = {
                    'total_count': data['total_count'],
                    'error_count': data['error_count'],
                    'success_rate': (data['total_count'] - data['error_count']) / data['total_count'] if data['total_count'] > 0 else 1.0
                avg_time = data['total_time'] / data['total_count'] if data['total_count'] > 0 else 0.0
                result[field] = {
                    'total_count': data['total_count'],
                    'error_count': data['error_count'],
                    'success_rate': f"{success_rate:.2%}",
                    'avg_time_ms': avg_time * 1000
                }
            return result

# 实例化并使用
metrics_collector = ValidationMetrics()

@app.route("/metrics")
@require_admin  # 假设这是一个需要管理员权限的端点
def show_metrics():
    """暴露监控指标"""
    return jsonify(metrics_collector.get_metrics())

# 在验证逻辑中集成指标收集
def monitored_validate(value, name):
    start_time = time.time()
    try:
        validated_value = validate(value, name).required().unwrap()
        duration = time.time() - start_time
        metrics_collector.record_validation(name, True, duration)
        return validated_value
    except ValidationError:
        duration = time.time() - start_time
        metrics_collector.record_validation(name, False, duration)
        raise
```

## API 参考

### `Validator` 类

链式验证器的核心。通过 `validate(value, name)` 函数创建。

- `required(message=None, code=None)`: 验证值不为 `None`。
- `not_empty(message=None, code=None)`: 验证字符串、列表、字典不为空。
- `min_length(length, message=None, code=None)`: 验证字符串或列表的最小长度。
- `max_length(length, message=None, code=None)`: 验证字符串或列表的最大长度。
- `is_valid_object_id(message=None, code=None)`: 验证值是合法的MongoDB ObjectId字符串。
- `custom(validator_func, message, code=None)`: 使用自定义函数进行验证。`validator_func` 接受值作为参数，返回 `True` 或 `False`。
- `result()`: 返回一个 `ValidationResult` 对象，包含值或错误。
- `unwrap()`: 成功时返回值，失败时抛出 `ValidationError`。
- `unwrap_or(default_value)`: 成功时返回值，失败时返回指定的 `default_value`。

### `DatabaseHelper` 类

MongoDB集合操作的助手。通过 `db_helper(client, db_name, collection_name)` 函数创建。

**查询**
- `find_one_or_fail(filter, message=None, code=None, projection=None)`: 查找单个文档，未找到则抛出 `ValidationError` (404)。
- `find_many_or_empty(filter, **kwargs)`: 查找多个文档，未找到则返回空列表。接受 `limit`, `skip`, `sort` 等 `find()` 的参数。
- `find_many_or_fail(filter, message=None, code=None, **kwargs)`: 查找多个文档，未找到则抛出 `ValidationError` (404)。
- `exists(filter)`: 检查符合条件的文档是否存在，返回 `True` 或 `False`。
- `count_documents(filter)`: 统计符合条件的文档数量。
- `find_field_or_fail(filter, field_name, message=None, code=None)`: 查找单个文档的特定字段值，未找到或字段不存在则抛出 `ValidationError` (404)。
- `find_non_empty_field(filter, field_name, message=None, code=None)`: 查找特定字段，并确保其值不为空（非`None`, 非空字符串/列表/字典）。

**写入**
- `insert_one_and_return(document)`: 插入单个文档，并在文档中附加 `_id` 后返回。
- `insert_many_and_return(documents)`: 插入多个文档，并在每个文档中附加 `_id` 后返回。

**更新**
- `update_one_and_return(filter, update, return_updated=True)`: 更新单个文档。若 `return_updated` 为 `True`，返回更新后的文档；否则返回 `UpdateResult` 的字典表示。
- `update_many_and_count(filter, update)`: 更新多个文档，返回包含 `matched_count` 和 `modified_count` 的字典。

**删除**
- `delete_one_and_return(filter)`: 删除单个文档，并返回被删除的文档。
- `delete_many_and_count(filter, error_if_none_deleted=False)`: 删除多个文档，返回被删除的数量。若 `error_if_none_deleted` 为 `True` 且没有文档被删除，则抛出 `ValidationError` (404)。

### `ValidationError` 异常类

- `__init__(message, code="VALIDATION_ERROR", status_code=400)`: 构造函数。
- `to_response()`: 将异常转换为Flask `Response` 对象，内容为JSON格式的错误信息。

### 装饰器

- `@handle_api_exceptions(context_message)`: 捕获 `ValidationError` 和其他异常，返回统一的JSON错误响应。
- `@handle_database_exceptions`: 捕获常见的PyMongo异常（如`DuplicateKeyError`, `ConnectionFailure`），并将其转换为 `ValidationError`。
- `@validate_request(*required_fields)`: 自动验证请求体（JSON）中是否存在指定的 `required_fields`，并将原始数据和已验证数据注入到视图函数中。

## 扩展框架

### 1. 添加自定义验证器方法

通过继承 `Validator` 类，可以轻松添加可复用的验证方法。

```python
import re

EMAIL_PATTERN = re.compile(r'^[^@]+@[^@]+\.[^@]+$')

class CustomValidator(Validator):
    def is_email(self, message=None, code=None):
        """验证邮箱格式"""
        is_valid = isinstance(self.value, str) and EMAIL_PATTERN.match(self.value)
        return self.add_error_if_invalid(
            is_valid,
            message or "邮箱格式不正确",
            code or "INVALID_EMAIL"
        )

    def is_strong_password(self, message=None, code=None):
        """验证强密码（至少8位，包含大写字母和数字）"""
        pwd = self.value
        is_valid = (
            isinstance(pwd, str) and
            len(pwd) >= 8 and
            any(c.isupper() for c in pwd) and
            any(c.isdigit() for c in pwd)
        )
        return self.add_error_if_invalid(
            is_valid,
            message or "密码必须至少8位，且包含大写字母和数字",
            code or "WEAK_PASSWORD"
        )

# 创建一个使用新验证器的工厂函数
def custom_validate(value, name):
    return CustomValidator(value, name)

# 使用
email = custom_validate(data.get("email"), "邮箱").required().is_email().unwrap()
password = custom_validate(data.get("password"), "密码").required().is_strong_password().unwrap()
```

### 2. 添加自定义数据库助手方法

通过继承 `DatabaseHelper`，可以封装特定集合的常用业务逻辑。

```python
class UserHelper(DatabaseHelper):
    def __init__(self, mongo_client):
        # 初始化时直接指定数据库和集合
        super().__init__(mongo_client["myapp"]["users"])

    def find_active_users(self, limit=50, skip=0):
        """查找所有活跃用户"""
        return self.find_many_or_empty(
            {"status": "active"},
            projection={"password": 0},
            limit=limit,
            skip=skip
        )
    
    def archive_user(self, user_id):
        """归档用户并返回更新后的用户"""
        return self.update_one_and_return(
            {"_id": ObjectId(user_id)},
            {"$set": {"status": "archived", "archived_at": datetime.utcnow()}},
            return_updated=True
        )

# 使用
user_helper = UserHelper(mongo_client)
active_users = user_helper.find_active_users()
archived_user = user_helper.archive_user(user_id)
```

## 结语

本指南全面介绍了验证框架的各项功能，从基础概念到高级应用和最佳实践。该框架旨在通过提供一套声明式、链式和高度可扩展的工具，简化和标准化Web应用中的数据验证和数据库交互。我们鼓励您在项目中积极使用，并根据业务需求对其进行扩展，以实现更高效、更健壮的开发流程。