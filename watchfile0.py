# watchfile.py (去抖 + 文件名过滤版)
import asyncio
import websockets
import threading
import time
import os
import fnmatch  # <-- 1. 导入 fnmatch 模块
from dotenv import load_dotenv
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

load_dotenv()

# --- 配置 ---
WATCH_FOLDER = os.environ.get("WATCH_FOLDER", "./001005AD/work_data/12345/image_data")
WEBSOCKET_HOST = os.environ.get("WEBSOCKET_HOST", "localhost")
WEBSOCKET_PORT = os.environ.get("WEBSOCKET_PORT", "8765")
# 2. 定义我们想要匹配的文件模式
FILE_PATTERN = os.environ.get("FILE_PATTERN", "cart_*.png")

# --- WebSocket 逻辑 (保持不变) ---
CONNECTED_CLIENTS = set()


async def register(websocket):
    CONNECTED_CLIENTS.add(websocket)
    print(
        f"新客户端连接: {websocket.remote_address}. 当前连接数: {len(CONNECTED_CLIENTS)}"
    )


async def unregister(websocket):
    CONNECTED_CLIENTS.remove(websocket)
    print(
        f"客户端断开: {websocket.remote_address}. 当前连接数: {len(CONNECTED_CLIENTS)}"
    )


async def send_to_all_clients(message):
    if CONNECTED_CLIENTS:
        await asyncio.gather(*[client.send(message) for client in CONNECTED_CLIENTS])


async def websocket_handler(websocket):
    await register(websocket)
    try:
        await websocket.wait_closed()
    finally:
        await unregister(websocket)


# --- 文件监控逻辑 (已添加文件名过滤) ---


class FileChangeHandler(FileSystemEventHandler):
    def __init__(self, loop):
        self.loop = loop
        self.DEBOUNCE_DELAY = 0.3
        self.debounce_timers = {}

    def process_event(self, event_path):
        try:
            print(f"稳定后处理文件: {event_path}")
            time.sleep(0.1)
            with open(event_path, "rb") as f:
                content = f.read()

            asyncio.run_coroutine_threadsafe(send_to_all_clients(content), self.loop)
            print(
                f"已将 '{os.path.basename(event_path)}' 的内容推送给 {len(CONNECTED_CLIENTS)} 个客户端。"
            )
        except FileNotFoundError:
            print(f"处理时文件已不存在: {event_path}")
        except Exception as e:
            print(f"读取或发送文件时出错: {e}")

    def debounce_event(self, event):
        if event.is_directory:
            return

        path = event.src_path
        filename = os.path.basename(path)

        # --- 3. 添加过滤逻辑 ---
        # 检查当前事件的文件名是否匹配我们定义的模式
        if not fnmatch.fnmatch(filename, FILE_PATTERN):
            print(f"忽略文件 (不匹配 '{FILE_PATTERN}'): {filename}")
            return  # 如果不匹配，则直接返回，不进行任何处理
        # --- 过滤逻辑结束 ---

        if path in self.debounce_timers:
            self.debounce_timers[path].cancel()

        timer = threading.Timer(self.DEBOUNCE_DELAY, self.process_event, args=[path])

        self.debounce_timers[path] = timer
        timer.start()

    def on_created(self, event):
        print(f"检测到创建事件: {event.src_path}")
        self.debounce_event(event)

    def on_modified(self, event):
        print(f"检测到修改事件: {event.src_path}")
        self.debounce_event(event)


# --- 其他代码保持不变 ---


def start_file_watcher(loop):
    print(f"文件监控器开始在独立线程中监控: {os.path.abspath(WATCH_FOLDER)}")
    print(f"模式过滤: 只处理符合 '{FILE_PATTERN}' 的文件")  # 添加提示
    event_handler = FileChangeHandler(loop)
    observer = Observer()
    observer.schedule(event_handler, WATCH_FOLDER, recursive=True)
    observer.start()
    observer.join()


async def main():
    loop = asyncio.get_running_loop()
    watcher_thread = threading.Thread(
        target=start_file_watcher, args=(loop,), daemon=True
    )
    watcher_thread.start()
    print(f"WebSocket 服务器将在 ws://{WEBSOCKET_HOST}:{WEBSOCKET_PORT} 上启动")
    async with websockets.serve(websocket_handler, WEBSOCKET_HOST, WEBSOCKET_PORT):
        print("服务器已成功启动，正在等待连接...")
        await asyncio.Future()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n检测到 Ctrl+C，服务器正在关闭。")
