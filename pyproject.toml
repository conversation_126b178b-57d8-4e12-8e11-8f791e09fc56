[project]
name = "backend"
version = "0.1.0"
description = "Backend for the project"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "bcrypt>=4.3.0",
    "bson>=0.5.10",
    "dotenv>=0.9.9",
    "flask-cors>=6.0.0",
    "flask-jwt-extended>=4.7.1",
    "flask-pymongo>=3.0.1",
    "matplotlib>=3.10.3",
    "numpy>=2.3.0",
    "openpyxl>=3.1.5",
    "pandas>=2.3.0",
    "pandas-stubs>=2.2.3.250527",
    "pydantic>=2.11.7",
    "python-snappy>=0.7.3",
    "returns>=0.25.0",
    "scipy>=1.15.3",
    "tifffile>=2025.6.1",
    "watchdog>=6.0.0",
    "websockets>=15.0.1",
]

[[tool.uv.index]]
url = "http:/pypi.tuna.tsinghua.edu.cn/simple" 
default = true
