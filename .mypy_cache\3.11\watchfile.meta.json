{"data_mtime": 1754490008, "dep_lines": [7, 13, 14, 1, 2, 3, 4, 5, 6, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 10, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["config.logging_config", "watchdog.observers", "watchdog.events", "os", "time", "asyncio", "threading", "fnmatch", "typing", "websockets", "builtins", "_frozen_importlib", "_typeshed", "abc", "asyncio.events", "logging", "types", "typing_extensions", "watchdog"], "hash": "4cfd0843aca6029bf3911b8fa1d2796a78f95e29", "id": "watchfile", "ignore_all": true, "interface_hash": "b3cb3176b562c06c23a92125ca6da0b4aa85c8d6", "mtime": 1754489977, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\Arcweb\\backend\\watchfile.py", "plugin_data": null, "size": 9248, "suppressed": [], "version_id": "1.15.0"}