{"data_mtime": 1754495214, "dep_lines": [41, 42, 43, 44, 45, 8, 15, 58, 220, 1, 5, 6, 7, 10, 11, 63, 219, 221, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 10, 10, 5, 5, 5, 10, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["my_code.web_code.user", "my_code.web_code.radar_manage", "my_code.web_code.data_analysis", "my_code.web_code.radar_information", "my_code.web_code.scene_parameter", "config.config", "config.logging_config", "config.shared_config", "my_code.radar_code", "flask", "os", "json", "flask_jwt_extended", "validator", "bson", "services", "threading", "multiprocessing", "builtins", "_frozen_importlib", "_typeshed", "abc", "bson.objectid", "config", "datetime", "flask.app", "flask.blueprints", "flask.config", "flask.helpers", "flask.json", "flask.sansio", "flask.sansio.app", "flask.sansio.blueprints", "flask.sansio.scaffold", "flask.wrappers", "flask_jwt_extended.jwt_manager", "flask_jwt_extended.utils", "flask_jwt_extended.view_decorators", "json.decoder", "logging", "my_code", "types", "typing", "typing_extensions", "validator.validator_framework", "werkzeug", "werkzeug.datastructures", "werkzeug.datastructures.headers", "werkzeug.sansio", "werkzeug.sansio.response", "werkzeug.wrappers", "werkzeug.wrappers.response", "wsgiref", "wsgiref.types"], "hash": "6da3acd680c8124db69518e5c69ef82744102673", "id": "app", "ignore_all": false, "interface_hash": "79ba818b6e976298b37a20ace1ee73150b8f0648", "mtime": 1754495577, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\Arcweb\\backend\\app.py", "plugin_data": null, "size": 8745, "suppressed": ["flask_cors"], "version_id": "1.15.0"}