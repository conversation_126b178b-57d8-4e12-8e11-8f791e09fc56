{"data_mtime": 1754494440, "dep_lines": [41, 42, 43, 44, 45, 8, 15, 58, 217, 1, 5, 6, 7, 10, 11, 216, 218, 240, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 10, 10, 5, 5, 5, 20, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["my_code.web_code.user", "my_code.web_code.radar_manage", "my_code.web_code.data_analysis", "my_code.web_code.radar_information", "my_code.web_code.scene_parameter", "config.config", "config.logging_config", "config.shared_config", "my_code.radar_code", "flask", "os", "json", "flask_jwt_extended", "validator", "bson", "threading", "multiprocessing", "services", "builtins", "_frozen_importlib", "_typeshed", "abc", "bson.objectid", "config", "datetime", "flask.app", "flask.blueprints", "flask.config", "flask.helpers", "flask.json", "flask.sansio", "flask.sansio.app", "flask.sansio.blueprints", "flask.sansio.scaffold", "flask.wrappers", "flask_jwt_extended.jwt_manager", "flask_jwt_extended.utils", "flask_jwt_extended.view_decorators", "json.decoder", "logging", "my_code", "types", "typing", "typing_extensions", "validator.validator_framework", "werkzeug", "werkzeug.datastructures", "werkzeug.datastructures.headers", "werkzeug.sansio", "werkzeug.sansio.response", "werkzeug.wrappers", "werkzeug.wrappers.response", "wsgiref", "wsgiref.types"], "hash": "6a567706104d1955ca6b451b78e13e8bfd0fcf5a", "id": "app", "ignore_all": false, "interface_hash": "af6caa04095a2a670a43e3ed9f44474420356774", "mtime": 1754494460, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\Arcweb\\backend\\app.py", "plugin_data": null, "size": 8752, "suppressed": ["flask_cors"], "version_id": "1.15.0"}