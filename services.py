# file: services.py

from watchfile import FileWatcher
import os

# --- 创建 FileWatcher 的全局单例实例 ---
# 这段代码在应用启动、第一次导入 services 模块时执行一次
# 确保了在整个应用的生命周期中，只有一个 FileWatcher 实例存在

# 使用全局变量来存储单例实例
_file_watcher_instance = None


def get_file_watcher():
    """获取 FileWatcher 单例实例"""
    global _file_watcher_instance

    if _file_watcher_instance is None:
        print("正在初始化 FileWatcher 服务...")

        # 在创建实例时，就可以从环境变量加载初始配置
        # 也可以在这里设置一个默认的"安全"配置
        os.environ.setdefault("WATCH_FOLDER", "logs_default")
        os.environ.setdefault("FILE_PATTERN", "*.log")
        os.environ.setdefault("WEBSOCKET_HOST", "localhost")
        os.environ.setdefault("WEBSOCKET_PORT", "8765")

        _file_watcher_instance = FileWatcher()

        print("FileWatcher 服务已成功初始化。")

    return _file_watcher_instance


# 创建全局实例
file_watcher = get_file_watcher()
