# file: services.py

from watchfile import FileWatcher
import os

# --- 创建 FileWatcher 的全局单例实例 ---
# 这段代码在应用启动、第一次导入 services 模块时执行一次
# 确保了在整个应用的生命周期中，只有一个 FileWatcher 实例存在
print("正在初始化 FileWatcher 服务...")

# 在创建实例时，就可以从环境变量加载初始配置
# 也可以在这里设置一个默认的“安全”配置
os.environ.setdefault("WATCH_FOLDER", "logs_default")
os.environ.setdefault("FILE_PATTERN", "*.log")
os.environ.setdefault("WEBSOCKET_HOST", "localhost")
os.environ.setdefault("WEBSOCKET_PORT", "8765")

file_watcher = FileWatcher()

# (可选) 如果您还有其他全局服务，比如数据库连接池、缓存客户端等
# 也可以在这里初始化并作为单例暴露出去
# from database import db_connection
# db = db_connection()

print("FileWatcher 服务已成功初始化。")
