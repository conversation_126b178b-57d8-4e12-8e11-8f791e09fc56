"""
数据库以及集合的配置, 与database.py管理数据库连接有区别
共享配置模块, 专提供给API使用z
包含数据库连接、集合对象和其他共享资源，用于解决循环导入问题
"""

from typing import Optional
from .database import mongo_connector
from validator import get_collection, DatabaseHelper
from enum import StrEnum
from pymongo import MongoClient
from pymongo.database import Database
from .logging_config import get_api_logger
from dotenv import load_dotenv
from flask import Response
from typing import Tuple, Dict, Any
import os

load_dotenv()

# 定义返回 api 的数据类型
ApiResponse = Tuple[Response, int]
Document = Dict[str, Any]

# 配置API模块日志器
logger = get_api_logger()

# 数据库相关常量
BASEDATA = os.environ.get("MONGO_DB_NAME", "base_data")


class BaseData(StrEnum):
    RADAR = "radar"
    USER = "users"
    SCENE = "scene"


# 全局变量，在初始化后设置
client: Optional[MongoClient[Document]] = None
radar_collection: Optional[DatabaseHelper] = None
user_collection: Optional[DatabaseHelper] = None
scene_collection: Optional[DatabaseHelper] = None


def init_shared_config(app):
    """
    初始化共享配置
    在Flask应用和蓝图注册后调用
    """
    global client, radar_collection, user_collection, scene_collection

    # 数据库连接
    logger.info("[API] 配置数据库并连接")
    mongo_connector.init_app(app)
    client = mongo_connector.client
    logger.info("[API] 数据库连接成功")

    logger.info("[API] 获取数据库连接")
    radar_collection = get_collection(client, BASEDATA, BaseData.RADAR.value)
    user_collection = get_collection(client, BASEDATA, BaseData.USER.value)
    scene_collection = get_collection(client, BASEDATA, BaseData.SCENE.value)

    logger.info("[API] 共享配置初始化完成")


def get_client() -> MongoClient:
    """获取MongoDB连接"""
    if client is None:
        raise RuntimeError("MongoDB连接未初始化，请先调用init_shared_config")
    return client


def get_radar_collection() -> DatabaseHelper:
    """获取雷达集合"""
    if radar_collection is None:
        raise RuntimeError("雷达集合未初始化，请先调用init_shared_config")
    return radar_collection


def get_user_collection() -> DatabaseHelper:
    """获取用户集合"""
    if user_collection is None:
        raise RuntimeError("用户集合未初始化，请先调用init_shared_config")
    return user_collection


def get_scene_collection() -> DatabaseHelper:
    """获取场景集合"""
    if scene_collection is None:
        raise RuntimeError("场景集合未初始化，请先调用init_shared_config")
    return scene_collection
